<template>
    <a-config-provider
        :locale="configLang"
        :transformCellText="({ text, column, record, index }) => text || '-'"
        autoInsertSpaceInButton
    >
        <!-- calc(100vh - 60px) -->
        <!--  style="min-height: 100vh; min-width: 100vh" -->
        <a-layout>
            <router-view />
        </a-layout>
    </a-config-provider>
</template>

<script>
import {
    defineComponent,
    provide,
    onBeforeMount,
    onMounted,
    ref,
    computed,
    onBeforeUnmount,
    onUnmounted,
} from 'vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
import deviceService from '@/apiService/device'
import { useStore } from 'vuex'
import hooks from './views/role/components/hooks'
import { setThemeColor, setBusinessType } from '@/common/util.js'
import Cookies from 'js-cookie'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { whiteRouteList } from '@/utils/whiteRouteList.js'

// import { fetchMessages } from '@/config/lang/i18nService.js'
export default defineComponent({
    name: 'app',
    setup() {
        const store = useStore()
        const { locale, setLocaleMessage } = useI18n()
        const router = useRouter()
        //获取用户信息
        store.commit('user/checkLogin')
        if (store.getters['user/isLogin']) {
            store.dispatch('user/getBasicInfo')
        }

        const loadMessages = async (lang) => {
            // const messages = await fetchMessages(lang)
            // setLocaleMessage(lang, messages)
        }
        let updateInterval
        const getConfigLang = (lang) => {
            if (lang == 'zh') {
                return zhCN
            } else if (lang == 'en') {
                return enUS
            } else {
                return zhCN
            }
        }
        const route = useRoute()
        const currentPage = computed(() => {
            return whiteRouteList.includes(route.path)
        })
        const configLang = ref(zhCN)
        onBeforeMount(async () => {
            locale.value = localStorage.getItem('language') || 'zh'
            await store.dispatch('lang/changeLanguage', locale.value)
            await loadMessages(locale.value)
            configLang.value = getConfigLang(locale.value)
            await router.isReady()
            console.log('currentPage.value------', currentPage.value, route)
            if (currentPage.value) {
                //
                hooks.setDeflutColor()
                store.commit('user/setConfigData', hooks.config)
            } else {
                await getDetail()
            }

            // store.dispatch('user/getUserColorDetail')
            updateInterval = setInterval(async () => {
                await loadMessages(locale.value)
            }, 30 * 60 * 1000) // 30分钟
        })
        // 加载语言包
        onUnmounted(() => {
            clearInterval(updateInterval)
        })

        provide(
            'ossCommonResource',
            'https://ming-enterprise-oss.mingwork.com/'
        )

        const getApi = async () => {
            const host = window.location.host
            const token = store.getters['user/getNewToken']
            if (token) {
                return await deviceService.getCurrentOrgData()
            } else {
                return await deviceService.getOrgWebpageSettingByDomain({
                    domain: host,
                })
            }
        }

        const getDetail = async () => {
            try {
                const {
                    data: { code, data },
                } = await getApi()
                if (code === 0) {
                    if (data) {
                        setBusinessType(data.businessType)
                        const params = {
                            orgLogo: data.orgLogo,
                            loginBanner: data.loginBanner,
                            themeColor: data.themeColor,
                            webPageIcon: data.webPageIcon,
                            webPageTitle: data.webPageTitle,
                        }
                        const obj = hooks.setColor(params)
                        document.getElementById('title').innerHTML =
                            obj.webPageTitle
                        document.getElementById('icon-svg').href =
                            obj.webPageIcon
                        document
                            .getElementsByTagName('body')[0]
                            .style.setProperty('--themeColor', obj.themeColor)
                        store.commit('user/setConfigData', obj)
                        setThemeColor(obj.themeColor)
                    } else {
                        hooks.setDeflutColor()
                        store.commit('user/setConfigData', hooks.config)
                    }
                } else {
                    hooks.setDeflutColor()
                    store.commit('user/setConfigData', hooks.config)
                }
            } catch (error) {
                hooks.setDeflutColor()
                store.commit('user/setConfigData', hooks.config)
            }
        }

        // 页面销毁时
        onBeforeUnmount(() => {
            Cookies.set('hideOnce', 0, {
                expires: 7,
                path: '/',
                domain: 'ssnj.com',
            })
        })
        return {
            zhCN,
            enUS,
            configLang,
        }
    },
})
</script>
<style>
[v-cloak] {
    display: none;
}
</style>
