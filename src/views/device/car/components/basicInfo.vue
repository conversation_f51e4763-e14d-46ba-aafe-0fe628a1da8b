<template>
    <div class="pl-3">
        <div class="ul-box flex justify-between items-center pb-3">
            <ul class="flex items-center flex-1 w-0 title-family px-2.5">
                <li class="mr-4" style="">
                    <div class="text-sm text-secondar-text dark:text-60-dark">
                        {{ $t('Device No') }}：{{ data.bmsInfo?.sn }}
                        {{ $t('客户名称') }}：{{
                            data.bmsInfo?.customerName || '-'
                        }}
                    </div>
                    <!-- <div
                        class="text-sm text-secondar-text dark:text-60-dark"
                    ></div> -->
                </li>
            </ul>
            <!-- <el-button plain round @click="lookBattery" linear disabled>{{
                    $t('设备二维码')
                }}</el-button> -->
            <el-button
                plain
                round
                @click="openQRCode"
                v-if="data.bmsInfo?.qrCodeUrl"
            >
                {{ $t('QR Code') }}
                <iconSvg name="qrcode" :class="'w-4 h-4 ml-1'" />
            </el-button>
        </div>

        <!-- 二维码弹窗 -->
        <el-drawer
            v-model="qrCodeVisible"
            title="设备二维码"
            direction="rtl"
            :size="486"
            :show-close="false"
            @close="qrCodeVisible = false"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>设备二维码</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="qrCodeVisible = false">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="downloadQRCode"
                            >{{ $t('Download') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div
                v-if="data.bmsInfo"
                class="flex flex-col items-center qrcode-text"
            >
                <p class="mt-5 text-base">设备二维码</p>
                <p class="mt-2 mb-4">设备编号: {{ data.bmsInfo?.sn }}</p>
                <img
                    style="width: 250px; height: 250px"
                    :src="data.bmsInfo?.qrCodeUrl"
                    alt=""
                    srcset=""
                />
            </div>
        </el-drawer>
        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Vehicle type') }}：
                </div>
                <div class="descriptions-content">
                    {{ getVehicleType(data.projectInfo?.vehicleType) }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('BatteryNo') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.batteryNo || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('car_leijixunhuancishu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.cycleCount
                            ? data.bmsInfo?.cycleCount
                            : '0'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Cell type') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.cellType }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Device model') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.model }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('charging time') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.chgTimeSum || 0 }} h
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('CellPackaging') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.cellPack }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('HWID') }}：
                </div>
                <div class="descriptions-content">
                    <el-tooltip
                        v-if="data.bmsInfo?.hwid"
                        class="box-item"
                        :effect="isDark ? 'dark' : 'light'"
                        :content="data.bmsInfo?.hwid"
                        placement="top-start"
                    >
                        <a class="tag cursor-pointer w-0">{{
                            data.bmsInfo?.hwid || '-'
                        }}</a>
                    </el-tooltip>
                    <span v-else>-</span>
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Discharging Time') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.dsgTimeSum || 0 }} h
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('BatteryNumber') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.tanks || 0 }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('IMEI') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.imei || '-' }}
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('AccumulatedChargingCapacity') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.chgCapSum || 0 }} Ah
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Individual voltage') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.projectInfo?.cellVoltage
                            ? data.projectInfo?.cellVoltage
                            : '0'
                    }}
                    V
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('ICCID') }}：
                </div>
                <div class="descriptions-content">
                    <el-tooltip
                        v-if="data.bmsInfo?.iccid"
                        class="box-item"
                        :effect="isDark ? 'dark' : 'light'"
                        :content="data.bmsInfo?.iccid"
                        placement="top-start"
                    >
                        <a class="tag cursor-pointer w-0">{{
                            data.bmsInfo?.iccid || '-'
                        }}</a>
                    </el-tooltip>
                    <span v-else>-</span>
                </div>
            </div>
            <div class="descriptions-item">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('AccumulatedDischargingCapacity') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.dsgCapSum || 0 }} Ah
                </div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('RatedTotalVoltage') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.ratedVoltage || 0 }} V
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('software_version') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.bmsInfo?.softwareVersion || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('manufacture_date') }}：
                </div>
                <div class="descriptions-content">-</div>
            </div>

            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('rated_total_current') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.ratedCurrent || 0 }} A
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <!--  :class="locale" <div class="descriptions-item-label">
                    {{ $t('BMS出厂报告') }}：
                </div>
                <div class="descriptions-content">-</div> -->
            </div>
            <!-- <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('service_expire_time') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.serviceExpireDate
                            ? dayjs(data.bmsInfo?.serviceExpireDate).format(
                                  'YYYY/MM/DD'
                              )
                            : '-'
                    }}
                </div>
            </div> -->
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('activation_date') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.activeTime
                            ? dayjs(data.bmsInfo?.activeTime).format(
                                  'YYYY/MM/DD'
                              )
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('car_edingrongliang') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.ratedCapacity || 0 }} Ah
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label"></div>
                <div class="descriptions-content"></div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('service_expire_time') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.bmsInfo?.serviceExpireDate
                            ? dayjs(data.bmsInfo?.serviceExpireDate).format(
                                  'YYYY/MM/DD'
                              )
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-3">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('rated_energy') }}：
                </div>
                <div class="descriptions-content">
                    {{ data.projectInfo?.ratedEnergy || 0 }} kWh
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, batteryStatus } from '../../const'
// import { getState } from '@/common/util.js'
import { computed, onMounted, ref } from 'vue'
import store from '@/store'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import iconSvg from '@/components/svgIcon'
export default {
    name: 'bmsBox',
    components: {
        iconSvg,
    },
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props, { emit }) {
        const { t, locale } = useI18n()
        const lookBattery = () => {
            emit('lookBattery', true)
        }
        const vehicleTypeOptions = computed(() => {
            return store.state.dictionary.dictionaries.vehicleType || []
        })
        const getVehicleType = (val) => {
            if (vehicleTypeOptions.value.length == 0) return
            if (!val) return '-'
            const found = vehicleTypeOptions.value.find(
                (item) => item.value == val
            )
            return found ? found.label : '-'
        }

        onMounted(async () => {
            await store.dispatch('dictionary/getDictionary', 'vehicleType')
        })

        const chargeState = ref([
            {
                label: t('status_chongdianzhong'),
                name: t('status_chongdian'),

                value: '1',
                icon: 'icon-ica-dianchi-fangdian',
                color: '#73ADFF',
                backGroundColor: '#73ADFF',
            },
            {
                label: t('status_fangdianzhong'),
                name: t('status_fangdian'),
                value: '2',
                icon: 'icon-ica-dianchi-chongdian',
                color: '#33BE4F',
                backGroundColor: '#33BE4F',
            },
            {
                label: t('status_daiji'),
                name: t('status_daiji'),
                value: '0',
                icon: 'icon-ica-dianchi-yunhang',
                color: '#222222',
                backGroundColor: '#d9d9d9',
            },
            {
                label: t('status_lixian'),
                name: t('status_lixian'),
                value: '3',
                icon: 'icon-ica-dianchi-lixian',
                color: '#666666',
                backGroundColor: '#666666',
            },
        ])
        const getState = (status) => {
            const item = chargeState.value.find((s) => s.value == status) || {}
            return item
        }
        const isDark = computed(() => {
            return store.state.theme.isDark
        })

        const qrCodeVisible = ref(false)
        const openQRCode = () => {
            qrCodeVisible.value = true
        }
        const downloadQRCode = () => {
            if (props.data.bmsInfo?.qrCodeUrl) {
                window.open(props.data.bmsInfo.qrCodeUrl, '_blank')
            }
        }

        return {
            accountUnit,
            batteryStatus,
            lookBattery,
            getState,
            getVehicleType,
            vehicleTypeOptions,
            dayjs,
            isDark,
            locale,
            qrCodeVisible,
            openQRCode,
            downloadQRCode,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;

    font-size: 14px;
    font-family: AlibabaPuHuiTi_2_55_Regular;

    .descriptions-item-label {
        color: var(--text-60);
        padding-left: 22px;
        // width: 60%;
        max-width: 70%;
        &.en {
            padding-left: 12px;
        }
    }

    .descriptions-content {
        color: var(--text-100);
        flex: 1;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &:nth-child(3n-2) {
        .descriptions-item-label {
            padding-left: 9px;
        }
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
    &.origin-gray {
        background-color: rgba(34, 34, 34, 0.5);
    }
}

.ul-box {
    // height: 40px;
    padding: 10px 0;
    // line-height: 40px;
    line-height: 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 16px;
    // background: var(--bg-f5);
}
.tag {
    // color: var(--themeColor);
}
</style>
