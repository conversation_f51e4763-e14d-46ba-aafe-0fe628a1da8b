<template>
    <div class="de">
        <!-- -->
        <div class="flex items-center justify-between">
            <div>
                <div
                    class="cursor-pointer leading-5.5 flex items-center orgName"
                    @click="orgVisible = true"
                >
                    <span class="flex mr-1">
                        <div class="w-5 h-5 rounded btn">
                            <iconSvg
                                name="treeOpen"
                                class="w-5 h-5"
                                className="tree-svg-box"
                            />
                        </div>
                    </span>
                    <span>{{ orgNameValue }}</span>
                </div>
            </div>
            <div class="flex gap-x-3">
                <el-button plain round @click="goOperationCar">
                    <span> {{ $t('Management') }}</span>
                    <iconSvg name="operation" class="w-5 h-5 ml-0.5" />
                </el-button>
                <el-button plain round>
                    {{ $t('Dashboard') }}
                    <iconSvg name="fullicon" class="w-5 h-5 ml-0.5" />
                </el-button>
            </div>
        </div>
        <div class="modules flex items-start justify-between gap-x-4">
            <div
                class="w-2/5 flex justify-end items-center rounded-lg bg-ff cursor-pointer"
                @click="changeTab('a')"
            >
                <div class="w-4/6 px-4 pt-3 pb-5">
                    <div
                        class="flex items-end orgName rounded px-3 py-2"
                        style="background: rgba(149, 158, 195, 0.1)"
                    >
                        <div class="leading-6 text-base">
                            {{ $t('Total devices') }}
                        </div>
                        <div class="text-3.5xl leading-8 font-bold ml-3">
                            {{ onesPieceData?.totalDeviceQuantity || 0 }}
                        </div>
                        <div>{{ $t('common_zuo') }}</div>
                    </div>
                    <div class="flex justify-between items-center px-3 mt-2">
                        <div class="">
                            <div class="mb-3">
                                <span class="text-60"
                                    >{{ $t('home_fugaichengshi') }}：</span
                                >
                                <span
                                    class="text-base leading-none font-medium text-100"
                                >
                                    {{ onesPieceData?.cityNum || 0 }}</span
                                ><span class="ml-1 font-medium text-100">{{
                                    $t('common_ge')
                                }}</span>
                            </div>
                            <div>
                                <span>{{ $t('Total capacity') }}：</span>
                                <span
                                    class="text-base leading-none font-medium text-100"
                                >
                                    {{ formatterAh(2222 || 0).num }}</span
                                ><span class="ml-1 font-medium text-100"
                                    >{{ formatterAh(2222 || 0).unit }}Ah</span
                                >
                            </div>
                        </div>
                        <div>
                            <div
                                class="device-num-tag online items-center mb-2"
                            >
                                <span>{{ $t('在线') }}</span
                                ><span
                                    class="font-medium inline-block text-right num"
                                    style="min-width: 17px"
                                    >{{
                                        onesPieceData?.onlineDeviceQuantity || 0
                                    }}</span
                                ><span>{{ $t('common_zuo') }}</span>
                            </div>
                            <div class="device-num-tag offline items-center">
                                <span>{{ $t('离线') }}</span
                                ><span
                                    class="font-medium inline-block text-right num"
                                    style="min-width: 17px"
                                    >{{
                                        onesPieceData?.offlineDeviceQuantity ||
                                        0
                                    }}</span
                                ><span>{{ $t('common_zuo') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="w-3/5 px-4 py-3 rounded-lg bg-ff dark:bg-ff-dark"
                @click="changeTab('b')"
            >
                <statistics />
            </div>
        </div>
        <div>
            <a-tabs class="w-full tabs pt-1" :activeKey="activeKey">
                <a-tab-pane key="a" tab="a">
                    <!-- 选项卡一：设备列表 -->
                    <div class="device-list-search flex items-center mb-4">
                        <el-select
                            v-model="selectedDeviceName"
                            :options="deviceNameOptions"
                            style="width: 140px; margin-right: 12px"
                            disabled
                        >
                            <el-option
                                v-for="item in deviceNameOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                        <el-input
                            v-model="keyword"
                            :placeholder="$t('请输入设备名称')"
                            style="width: 220px; margin-right: 12px"
                            @keyup.enter="handleSearch"
                            clearable
                        />
                        <el-button type="primary" @click="handleSearch">{{
                            $t('搜索')
                        }}</el-button>
                    </div>
                    <div v-if="loading" class="text-center py-10">
                        <el-spin />
                    </div>
                    <div v-else>
                        <div v-if="deviceList.length" class="device-table-list">
                            <div
                                v-for="item in deviceList"
                                :key="item.id"
                                class="device-table-row flex items-center bg-ff rounded-lg mb-3 px-4 py-3 cursor-pointer hover:shadow border border-transparent hover:border-themeColor transition"
                                @click="handleRowClick(item)"
                            >
                                <div
                                    class="w-20 h-16 flex items-center justify-center mr-6"
                                >
                                    <carImg
                                        :vehicleType="
                                            getSafeValue(
                                                item.vehicleType,
                                                'default'
                                            )
                                        "
                                    />
                                </div>
                                <div
                                    class="flex-1 flex flex-col justify-between"
                                >
                                    <div class="flex items-center mb-1">
                                        <span class="font-bold text-base mr-4"
                                            >{{ $t('车辆编号') }}：{{
                                                getSafeValue(item.sn)
                                            }}</span
                                        >
                                        <span class="text-xs text-60"
                                            >({{ $t('电池编号') }}:
                                            {{
                                                getSafeValue(item.batterySn)
                                            }})</span
                                        >
                                        <span class="text-xs text-60 ml-4"
                                            >HWID:
                                            {{ getSafeValue(item.hwid) }}</span
                                        >
                                    </div>
                                    <div class="flex items-center gap-x-6 mb-1">
                                        <span class="text-sm"
                                            >{{ $t('BMS型号') }}:
                                            <b>{{
                                                getSafeValue(item.bmsModel)
                                            }}</b></span
                                        >
                                        <span class="text-sm"
                                            >{{ $t('容量') }}:
                                            <b
                                                >{{
                                                    getSafeValue(item.capacity)
                                                }}
                                                Mwh</b
                                            ></span
                                        >
                                        <span class="text-sm"
                                            >{{ $t('充电时长') }}:
                                            <b
                                                >{{
                                                    getSafeValue(
                                                        item.chargeDuration
                                                    )
                                                }}
                                                h</b
                                            ></span
                                        >
                                        <span class="text-sm"
                                            >{{ $t('服务到期时间') }}:
                                            <b>{{
                                                getSafeValue(
                                                    item.serviceExpireDate
                                                )
                                            }}</b></span
                                        >
                                    </div>
                                    <div class="flex items-center gap-x-4">
                                        <span
                                            v-if="item.status"
                                            class="text-xs px-2 py-0.5 rounded bg-green-100 text-green-700"
                                            >{{
                                                $t(getSafeValue(item.status))
                                            }}</span
                                        >
                                        <span
                                            v-if="item.efficiency"
                                            class="text-xs px-2 py-0.5 rounded bg-green-50 text-green-600 border border-green-200"
                                            >{{ $t('运行效率') }}:
                                            {{
                                                getSafeValue(item.efficiency)
                                            }}</span
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>
                        <empty-data
                            v-else
                            :description="$t('zanwushuju')"
                            class="my-10"
                        />
                        <div class="flex justify-end mt-6">
                            <el-pagination
                                background
                                layout="prev, pager, next"
                                :total="pagination.total"
                                v-model:current-page="pagination.current"
                                :page-size="pagination.size"
                                @current-change="handlePageChange"
                            />
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="b" tab="b">
                    <!-- 选项卡二 -->
                    <div class="p-4 bg-ff dark:bg-ff-dark rounded-lg">
                        <div
                            class="flex justify-between items-center mb-6 text-title dark:text-title-dark"
                        >
                            <el-tabs
                                v-model="statisticsActiveName"
                                @tab-change="
                                    handleStatisticsActiveNameTabChange
                                "
                            >
                                <el-tab-pane
                                    :label="$t('Charge and discharge capacity')"
                                    name="a"
                                >
                                </el-tab-pane>
                                <el-tab-pane
                                    :label="$t('Average running time')"
                                    name="b"
                                >
                                </el-tab-pane>
                            </el-tabs>
                            <div class="flex justify-between gap-x-3">
                                <el-select
                                    v-model="projectChargeTotalDateType"
                                    placeholder="请选择"
                                    style="
                                        width: 120px;
                                        margin-right: 16px;
                                        border-radius: 8px;
                                    "
                                    @change="onProjectChargeTotalDateChange"
                                >
                                    <el-option
                                        v-for="item in chargeTotalDateOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                        </div>
                        <!-- 393px -->
                        <div class="flex gap-x-5">
                            <div class="flex-1 w-0">
                                <div
                                    class="w-full"
                                    style="height: 306px"
                                    id="incomeEcharts"
                                ></div>
                            </div>
                            <div class="rank">
                                <div
                                    class="text-secondar-text dark:text-60-dark"
                                >
                                    {{ $t('Charge/Discharge Ranking') }}
                                </div>
                                <div
                                    class="rank-header text-secondar-text dark:text-60-dark flex justify-between items-center text-center py-3"
                                >
                                    <div class="rank-num h-5.5">
                                        {{ $t('Rank') }}
                                    </div>
                                    <div class="rank-name">
                                        {{ $t('Device No') }}
                                    </div>
                                    <div
                                        class="flex flex-1 items-center justify-end"
                                    >
                                        <div>
                                            {{
                                                chargeType === 'charge'
                                                    ? t('Charge')
                                                    : t('Discharge')
                                            }}({{ profitUnit }})
                                        </div>
                                        <div
                                            class="w-5 h-5 flex items-center justify-center cursor-pointer select-none text-center ml-3"
                                            @click="toggleRank"
                                        >
                                            <iconSvg
                                                name="toggle"
                                                :className="'toggle'"
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="space-y-4">
                                    <template
                                        v-for="(item, index) in rankList"
                                        :key="index"
                                    >
                                        <div
                                            class="pr-1.5 flex items-center text-title dark:text-title-dark"
                                            v-if="index < 5"
                                        >
                                            <div class="h-5.5 rank-num">
                                                {{ index + 1 }}
                                            </div>
                                            <div
                                                class="rank-name"
                                                :title="item.key"
                                            >
                                                {{ item.key }}
                                            </div>
                                            <div
                                                class="flex-1 flex justify-end items-center w-0 rank-process"
                                            >
                                                <div
                                                    class="text-base leading-5.5 w-18 quantity text-right"
                                                >
                                                    {{ item.quantity }}
                                                </div>
                                                <!-- <div class="w-25">
                                                        <BarProgress
                                                            :fullWidth="100"
                                                            :ratio="item.ratio"
                                                        />
                                                    </div> -->
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="!rankList?.length">
                                        <empty-data
                                            :description="$t('zanwushuju')"
                                            class="mx-auto mt-10 text-secondar-text dark:text-60-darl"
                                        >
                                            <slot name="empty"></slot>
                                        </empty-data>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-x-3 mt-3">
                        <div
                            class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                        >
                            <div
                                class="w-full flex justify-between items-center text-title dark:text-title-dark"
                            >
                                <div>
                                    {{ $t('Operating Hours Distribution') }}
                                </div>
                                <div></div>
                            </div>
                            <div class="w-full" style="height: 336px">
                                <distributionChart
                                    :chartData="distributionChartDataDur"
                                    type="hour"
                                />
                            </div>
                        </div>
                        <div
                            class="flex-1 bg-ff dark:bg-ff-dark rounded-lg p-4"
                        >
                            <div
                                class="w-full flex justify-between items-center text-title dark:text-title-dark"
                            >
                                <div>
                                    {{ $t('Service Life Distribution') }}
                                </div>
                                <div></div>
                            </div>
                            <div class="w-full" style="height: 336px">
                                <distributionChart
                                    :chartData="distributionChartDataYears"
                                    type="month"
                                />
                            </div>
                        </div>
                    </div>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
    <el-drawer
        v-model="orgVisible"
        direction="ltr"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ orgNameValue }}</span>
                </div>
                <div>
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                </div>
            </div>
        </template>
        <el-tree
            style="max-width: 600px"
            default-expand-all
            :data="treeData"
            :props="{ label: 'name', children: 'children' }"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            @node-click="handleNodeClick"
            :current-node-key="selectedKeys[0]"
        />
    </el-drawer>
</template>

<script setup>
import { onMounted, ref, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import powerApi from '@/apiService/power'
import {
    pieData,
    chargeOptions,
    chargeStatus,
    unitConversion,
    alternateUnits,
    formatterKw,
    formatterAh,
    getChargeOption,
    updateEcharts,
} from '../const'
import statistics from '../components/statistics.vue'
import carImg from './components/carImg.vue'
import emptyData from '@/components/emptyData.vue'
import distributionChart from './components/distributionChart.vue'
import debounce from 'lodash/debounce'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import moment from 'moment'
import _cloneDeep from 'lodash/cloneDeep'
const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const store = useStore()
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])

const orgNameValue = ref()
const orgVisible = ref(false)
const onClose = () => {
    orgVisible.value = false
}
const treeData = ref([])
const selectedKeys = ref()
const addDraggedProperty = (tree) => {
    function traverse(node) {
        if (node.children && node.children.length > 0) {
            node.children.forEach(traverse)
        }
    }
    if (tree?.length) {
        tree.forEach(traverse)
        return tree
    }
}
const getTreeData = async () => {
    const {
        data: { data, code },
    } = await apiService.getDeviceTree({
        businessType: 'vehicle_battery',
    })
    if (code === 0) {
        let rootNode = {
            name: getCompanyInfo?.value?.orgName,
            id: getCompanyInfo?.value?.orgId,
            province: getCompanyInfo?.value?.province,
        }
        const tree = [
            {
                ...rootNode,
                children: addDraggedProperty(data) || [],
            },
        ]
        //
        treeData.value = tree
        selectedKeys.value = store.state.car.selectSupplierInfoCar?.id
            ? [store.state.car.selectSupplierInfoCar?.id]
            : [treeData.value[0].id]
        let info = store.state.car.selectSupplierInfoCar?.id
            ? store.state.car.selectSupplierInfoCar
            : tree[0]
        const { id, name, province } = info
        store.commit('car/setSelectSupplierInfoCar', {
            id,
            name,
            province,
        })
        // 从树结构中获取当前节点以及下属数据
        // getChartDataByTreeSelectedKey(selectedKeys.value)
    }
}
const deviceList = ref([])
const loading = ref(false)
const keyword = ref('')
const deviceNameOptions = ref([{ value: 'deviceName', label: t('设备名称') }])
const selectedDeviceName = ref('deviceName')
const pageSize = 7
const pagination = ref({ current: 1, size: pageSize, total: 0 })

// 顶部统计数据
const onesPieceData = ref({
    totalDeviceQuantity: 0,
    cityNum: 0,
    onlineDeviceQuantity: 0,
    offlineDeviceQuantity: 0,
})

// 获取组织/项目/客户参数
const getContextParams = () => {
    // 优先从store获取
    const orgId = store.state.car?.selectSupplierInfoCar?.id || ''
    const projectId = route.query.projectId || ''
    const customerId = route.query.customerId || ''
    return { orgId, projectId, customerId }
}

// 字段安全获取
const getSafeValue = (val, defaultVal = '-') => {
    if (val === undefined || val === null || val === '') return defaultVal
    return val
}

const getDeviceList = async () => {
    loading.value = true
    try {
        const { orgId, projectId, customerId } = getContextParams()
        const params = {
            current: pagination.value.current,
            size: pagination.value.size,
            keyword: keyword.value,
            orgId,
            projectId,
            customerId,
        }
        const res = await powerApi.getDevicePageList(params)
        deviceList.value = res.data.data.records || []
        pagination.value.total = res.data.data.total || 0
    } catch (e) {
        deviceList.value = []
        pagination.value.total = 0
    } finally {
        loading.value = false
    }
}

// 获取顶部统计数据
const getStatisticsData = async () => {
    try {
        const { orgId, projectId, customerId } = getContextParams()
        // 这里应该调用实际的API获取统计数据
        // 暂时使用模拟数据
        onesPieceData.value = {
            totalDeviceQuantity: 156,
            cityNum: 12,
            onlineDeviceQuantity: 142,
            offlineDeviceQuantity: 14,
        }
    } catch (e) {
        console.error('获取统计数据失败:', e)
    }
}

const handleSearch = debounce(() => {
    pagination.value.current = 1
    getDeviceList()
}, 300)

const handlePageChange = debounce((page) => {
    pagination.value.current = page
    getDeviceList()
}, 300)

const handleRowClick = (item) => {
    if (!item.sn || !item.id || !item.customerId) {
        ElMessage.error(t('设备信息不完整，无法跳转'))
        return
    }
    router.push({
        name: 'equipmentDetail',
        query: {
            sn: item.sn,
            id: item.id,
            customerId: item.customerId,
        },
    })
}

const handleNodeClick = async (data) => {
    const { id, name, province } = data
    orgNameValue.value = name
    selectedKeys.value = [id]
    orgVisible.value = false
    store.commit('car/setSelectSupplierInfoCar', { id, name, province })
    // 获取列表

    if (getCompanyInfo.value?.orgName) {
        orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
            ? store.state.car.selectSupplierInfoCar?.name
            : getCompanyInfo.value.orgName
    }
    await getDeviceList()
}
const goOperationCar = () => {
    const origin = window.location.origin
    window.open(`${origin}/#/operation-car`)
}

// 选项卡切换
const activeKey = ref('a')
const changeTab = (key) => {
    activeKey.value = key
    if (key === 'b') {
        // 切换到选项卡二时初始化数据
        nextTick(() => {
            initTabTwoData()
        })
    }
}

// 选项卡二相关变量
const statisticsActiveName = ref('a')
const projectChargeTotalDateType = ref('1')
const chargeTotalDateOptions = ref([
    {
        label: t('common_jinyizhou'),
        value: '1',
    },
    {
        label: t('common_jinyinian'),
        value: '2',
    },
])
const chargeType = ref('charge')
const profitUnit = ref('Ah')
const rankList = ref([])
const distributionChartDataDur = ref([])
const distributionChartDataYears = ref([])

// 选项卡二方法
const handleStatisticsActiveNameTabChange = async () => {
    setChargeOptions()
}

const onProjectChargeTotalDateChange = async () => {
    chargeDateSearchChange()
}

const toggleRank = async () => {
    chargeType.value = chargeType.value === 'charge' ? 'discharge' : 'charge'
    await getRankData()
}

// 图表数据
const dateList2 = ref([])
const chargeData2 = ref([])
const dischargeData2 = ref([])
const chargeDur2 = ref([])
const disChargeDur2 = ref([])

const setChargeOptions = () => {
    let options = _cloneDeep(getChargeOption())
    if (dateList2.value?.length) {
        options.xAxis.data = dateList2.value
    } else {
        if (projectChargeTotalDateType.value === '1') {
            options.xAxis.data = Array.from({ length: 7 }, (_, i) => {
                return dayjs()
                    .subtract(6 - i, 'day')
                    .format('MM/DD')
            })
        } else if (projectChargeTotalDateType.value === '2') {
            options.xAxis.data = Array.from({ length: 12 }, (_, i) => {
                return dayjs()
                    .subtract(11 - i, 'month')
                    .format('YYYY-MM')
            })
        }
    }

    if (projectChargeTotalDateType.value === '1') {
        options.series[0].barWidth = '24px'
        options.series[1].barWidth = '24px'
    } else {
        options.series[0].barWidth = '18px'
        options.series[1].barWidth = '18px'
    }

    if (statisticsActiveName.value == 'a') {
        options.yAxis.name = 'Ah'
        options.series[0].data = chargeData2.value
        options.series[1].data = dischargeData2.value
    } else if (statisticsActiveName.value == 'b') {
        options.yAxis.name = 'h'
        options.series[0].data = chargeDur2.value
        options.series[1].data = disChargeDur2.value
        options.series[0].name = t('Charging duration')
        options.series[1].name = t('Discharging duration')
        options.legend.data = [
            t('Charging duration'),
            t('Discharging duration'),
        ]
    }
    updateEcharts('incomeEcharts', options)
}

const chargeDateSearchChange = async () => {
    // 模拟数据获取
    await getChargeData()
    await getRankData()
}

const getChargeData = async () => {
    // 这里应该调用实际的API获取数据
    // 暂时使用模拟数据
    const { orgId, projectId, customerId } = getContextParams()

    // 模拟数据
    if (projectChargeTotalDateType.value === '1') {
        dateList2.value = Array.from({ length: 7 }, (_, i) => {
            return dayjs()
                .subtract(6 - i, 'day')
                .format('MM/DD')
        })
        chargeData2.value = [120, 132, 101, 134, 90, 230, 210]
        dischargeData2.value = [220, 182, 191, 234, 290, 330, 310]
        chargeDur2.value = [2.4, 2.6, 2.0, 2.7, 1.8, 4.6, 4.2]
        disChargeDur2.value = [4.4, 3.6, 3.8, 4.7, 5.8, 6.6, 6.2]
    } else {
        dateList2.value = Array.from({ length: 12 }, (_, i) => {
            return dayjs()
                .subtract(11 - i, 'month')
                .format('YYYY-MM')
        })
        chargeData2.value = [
            1200, 1320, 1010, 1340, 900, 2300, 2100, 1800, 1900, 2000, 2200,
            2400,
        ]
        dischargeData2.value = [
            2200, 1820, 1910, 2340, 2900, 3300, 3100, 2800, 2900, 3000, 3200,
            3400,
        ]
        chargeDur2.value = [24, 26, 20, 27, 18, 46, 42, 38, 39, 40, 42, 44]
        disChargeDur2.value = [44, 36, 38, 47, 58, 66, 62, 58, 59, 60, 62, 64]
    }

    setChargeOptions()
}

const getRankData = async () => {
    const { orgId, projectId, customerId } = getContextParams()

    // 模拟排名数据
    const mockRankData = [
        { key: 'Device001', quantity: 1250 },
        { key: 'Device002', quantity: 1180 },
        { key: 'Device003', quantity: 1050 },
        { key: 'Device004', quantity: 980 },
        { key: 'Device005', quantity: 920 },
    ]

    rankList.value = mockRankData

    let max = 0
    rankList.value.forEach((item) => {
        if (+max < +item.quantity) max = +item.quantity
    })

    if (max) {
        rankList.value.forEach((item) => {
            item['ratio'] = +(item.quantity / max).toFixed(2)
        })
    }

    rankList.value.sort((a, b) => {
        return b.quantity - a.quantity
    })
}

const transformData = (arr, xKey, yKey) => {
    const maxX = Math.ceil(Math.max(...arr.map((item) => item[xKey])) * 1.2)

    const timeMap = arr.reduce((acc, curr) => {
        acc[curr[xKey]] = curr[yKey]
        return acc
    }, {})

    const result = Array.from({ length: maxX + 1 }, (_, index) => ({
        x: index,
        value: timeMap[index] || 0,
    }))
    return result
}

const getStatisticDeviceData = async () => {
    const { orgId, projectId, customerId } = getContextParams()

    // 模拟分布数据
    const mockTimeData = [
        { time: 1, quantity: 5 },
        { time: 2, quantity: 8 },
        { time: 3, quantity: 12 },
        { time: 4, quantity: 15 },
        { time: 5, quantity: 10 },
        { time: 6, quantity: 6 },
        { time: 7, quantity: 3 },
        { time: 8, quantity: 2 },
    ]

    const mockRunTimeData = [
        { time: 1, quantity: 3 },
        { time: 2, quantity: 6 },
        { time: 3, quantity: 9 },
        { time: 4, quantity: 12 },
        { time: 5, quantity: 8 },
        { time: 6, quantity: 4 },
        { time: 7, quantity: 2 },
    ]

    distributionChartDataYears.value = transformData(
        mockTimeData,
        'time',
        'quantity'
    )
    distributionChartDataDur.value = transformData(
        mockRunTimeData,
        'time',
        'quantity'
    )
}

const initTabTwoData = async () => {
    await getChargeData()
    await getRankData()
    await getStatisticDeviceData()
}

onMounted(async () => {
    await getTreeData()

    if (getCompanyInfo.value?.orgName) {
        orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
            ? store.state.car.selectSupplierInfoCar?.name
            : getCompanyInfo.value.orgName
    }
    getDeviceList()
    getStatisticsData()
})
</script>

<style lang="less" scoped>
.de {
    min-height: calc(100vh - 128px);
    padding-top: 88px;
}
.orgName {
    color: var(--text-80);
}

:deep(.el-tree-node:focus > .el-tree-node__content) {
    background-color: transparent;
}
:deep(.el-tree-node__content) {
    flex-direction: row-reverse;
    height: 40px;
    border-radius: 4px;
    .el-tree-node__label {
        line-height: 40px;
        color: var(--text-80);
    }
    .el-text {
        color: var(--text-80) !important;
    }
    &:hover {
        // background: #f5f7f7;
        background-color: var(--bg-f5f7fa);
        color: var(--themeColor);
        .el-tree-node__label {
            &::before {
                border-color: var(--themeColor);
            }
        }
        * {
            fill: var(--themeColor);
        }
        .el-tree-node__expand-icon::before {
            background: var(--themeColor);
        }
        .el-tree-node__expand-icon.expanded::before {
            background: var(--themeColor);
        }
    }
}
:deep(.el-tree-node__label) {
    flex: 1;
    position: relative;
    text-indent: 8px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-bottom-left-radius: 2px;
        border-left: 1px solid var(--border);
        border-bottom: 1px solid var(--border);
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
    }
}
:deep(.el-tree) {
    display: block;
}
.el-tree {
    display: block;
    > :deep(.el-tree-node) {
        > .el-tree-node__content {
            > .el-tree-node__label {
                display: block;

                &::before {
                    display: none;
                }
            }
        }
    }
}
:deep(
        .el-tree--highlight-current
            .el-tree-node.is-current
            > .el-tree-node__content
    ) {
    border-radius: 4px;
    > .el-icon {
        color: #fff;
        fill: var(--themeColor);
    }
}
:deep(.el-tree-node) {
    .el-tree-node__expand-icon {
        font-size: 20px;
        color: #fff;
    }

    .el-tree-node__expand-icon::before {
        content: '+';
        font-size: 12px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.expanded::before {
        content: '-';
        font-size: 14px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.is-leaf {
        display: none;
    }
    .el-tree-node__expand-icon.el-icon-caret-right {
        transform: none !important;
    }
    .el-tree-node__expand-icon {
        font-style: normal !important;
        transform: none !important;
        svg {
            display: none !important;
        }
    }
    &.is-current {
        > .el-tree-node__content {
            > .el-tree-node__label {
                color: var(--themeColor);
            }
            > .el-icon {
                color: #fff;
                &:before {
                    background: var(--themeColor);
                }
            }
        }
    }
}
.device-num-tag {
    // padding: 4px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 24px;
    align-items: center;
    padding: 0 15px;
    font-size: 0;
    height: 24px;
    span {
        font-size: 12px;
        vertical-align: baseline;
    }
    .num {
        font-size: 16px;
    }

    // vertical-align: text-bottom;
}
.online {
    background: var(--tag-online-bg);
    color: var(--tag-online-color);
}

.offline {
    background: var(--tag-offline-bg);
    color: var(--tag-offline-color);
}
.text-60 {
    color: var(--text-80);
}
.text-100 {
    color: var(--text-100);
}
.device-list-search {
    margin-bottom: 18px;
}
.device-table-list {
    min-height: 420px;
}
.device-table-row {
    transition: box-shadow 0.2s, border-color 0.2s;
}
.device-table-row:hover {
    border-color: var(--themeColor);
    box-shadow: 0 2px 12px 0 rgba(34, 34, 34, 0.08);
}

/* 选项卡二排名样式 */
.rank {
    width: 280px;
    flex-shrink: 0;
}

.rank-header {
    border-bottom: 1px solid var(--border);
}

.rank-num {
    width: 40px;
    text-align: center;
    flex-shrink: 0;
}

.rank-name {
    width: 120px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rank-process {
    min-width: 0;
}

.quantity {
    flex-shrink: 0;
}

:deep(.el-tabs__nav-wrap::after) {
    display: none;
}

:deep(.el-tabs__active-bar) {
    background-color: var(--themeColor);
}

:deep(.el-tabs__item.is-active) {
    color: var(--themeColor);
}

:deep(.el-tabs__item:hover) {
    color: var(--themeColor);
}
</style>
