<template>
    <!-- <mw-header class="relative" style="z-index: 100" /> -->
    <div class="site-h">
        <div id="containerCar" class="relative">
            <div class="relative h-full mx-auto list-content">
                <div class="absolute bottom-4 top-3 info">
                    <div
                        class="flex flex-col bg-ff dark:bg-ff-dark h-full content-list"
                    >
                        <div class="h-full flex flex-col">
                            <div class="px-3.5 pt-5">
                                <div
                                    class="info-header flex items-center justify-between"
                                >
                                    <div>
                                        <div
                                            class="cursor-pointer leading-5.5 flex items-center orgName"
                                            @click="visible = true"
                                        >
                                            <span>{{ orgNameValue }}</span>
                                            <span class="flex ml-1">
                                                <div
                                                    class="w-5 h-5 rounded btn"
                                                >
                                                    <iconSvg
                                                        name="treeOpen"
                                                        class="w-5 h-5"
                                                        className="tree-svg-box"
                                                    />
                                                </div>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <el-button
                                            v-if="false"
                                            plain
                                            round
                                            @click="openFilter"
                                            class="border-1 relative btn-hover mr-3"
                                        >
                                            <span>{{
                                                $t('shebeishaixuan')
                                            }}</span>
                                            <el-icon
                                                ><Filter
                                                    style="
                                                        width: 16px;
                                                        height: 16px;
                                                    "
                                            /></el-icon>
                                        </el-button>
                                        <div
                                            class="flex items-center cursor-pointer toggle"
                                            @click="toggleSystem"
                                            v-if="showToggleBtn"
                                        >
                                            <div>
                                                {{
                                                    activeSystem == 'device'
                                                        ? $t('system_chuneng')
                                                        : $t('system_dongli')
                                                }}
                                            </div>
                                            <iconSvg
                                                name="toggle"
                                                class="w-4 h-4 ml-1"
                                                :className="'login-out '"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="info-content relative flex-1 h-0 overflow-y-auto"
                                id="info-content"
                                ref="contentRef"
                            >
                                <div class="h-full flex flex-col">
                                    <div class="px-3.5">
                                        <div
                                            class="content-header px-1.5 flex justify-between items-end"
                                        >
                                            <div
                                                class="flex items-center total"
                                            >
                                                <div
                                                    class="h-8 rounded text-sm font-medium leading-8 flex items-end orgName"
                                                >
                                                    <iconSvg
                                                        name="icon5"
                                                        class="w-6 h-6 mb-0.5 mr-1 orgIcon"
                                                    />
                                                    <div
                                                        class="leading-6 text-base"
                                                    >
                                                        {{
                                                            $t('Total devices')
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-3.5xl leading-8 font-bold ml-3"
                                                    >
                                                        {{
                                                            onesPieceData?.totalDeviceQuantity ||
                                                            0
                                                        }}
                                                    </div>
                                                    <!-- <div
                                                        class="text-xs leading-5 ml-0.5"
                                                    >
                                                        {{ $t('common_zuo') }}
                                                    </div> -->
                                                </div>
                                            </div>
                                            <div
                                                class="inline-flex justify-center mb-1"
                                            >
                                                <div
                                                    class="device-num-tag online items-center"
                                                >
                                                    <span>{{ $t('激活') }}</span
                                                    ><span
                                                        class="ml-0.5 font-medium inline-block text-right"
                                                        style="min-width: 17px"
                                                        >{{
                                                            onesPieceData?.activeDeviceQuantity ||
                                                            0
                                                        }}</span
                                                    ><span>{{
                                                        $t('common_liang')
                                                    }}</span>
                                                </div>
                                                <div
                                                    class="device-num-tag offline items-center"
                                                >
                                                    <span>{{ $t('在线') }}</span
                                                    ><span
                                                        class="ml-0.5 font-medium inline-block text-right"
                                                        style="min-width: 17px"
                                                        >{{
                                                            onesPieceData?.onlineDeviceQuantity ||
                                                            0
                                                        }}</span
                                                    ><span>{{
                                                        $t('common_liang')
                                                    }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content-statistics px-1.5">
                                            <div
                                                class="flex gap-x-10 w-full justify-between pl-7"
                                            >
                                                <div>
                                                    <div class="leading-4 mb-1">
                                                        {{ $t('Total power') }}
                                                    </div>
                                                    <div
                                                        class="text-base font-medium"
                                                    >
                                                        <span
                                                            class="text-2xl"
                                                            >{{
                                                                formatterKw(
                                                                    onesPieceData?.ratedPower ||
                                                                        0
                                                                ).num
                                                            }}</span
                                                        ><span
                                                            class="text-xs ml-1"
                                                            >{{
                                                                formatterKw(
                                                                    onesPieceData?.ratedPower ||
                                                                        0
                                                                ).unit
                                                            }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="leading-4 mb-1">
                                                        {{
                                                            $t('Total capacity')
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-base font-medium"
                                                    >
                                                        <span class="text-2xl">
                                                            {{
                                                                formatterAh(
                                                                    onesPieceData?.ratedCapacity ||
                                                                        0
                                                                ).num
                                                            }} </span
                                                        ><span
                                                            class="text-xs ml-1"
                                                        >
                                                            {{
                                                                formatterAh(
                                                                    onesPieceData?.ratedCapacity ||
                                                                        0
                                                                ).unit
                                                            }}Ah</span
                                                        >
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="leading-4 mb-1">
                                                        {{
                                                            $t(
                                                                'home_fugaichengshi'
                                                            )
                                                        }}
                                                    </div>
                                                    <div
                                                        class="text-base font-medium"
                                                    >
                                                        <span class="text-2xl">
                                                            {{
                                                                onesPieceData?.cityNum ||
                                                                0
                                                            }}</span
                                                        ><span
                                                            class="text-xs ml-1"
                                                            >{{
                                                                $t('common_ge')
                                                            }}</span
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-1 relative px-3.5 pb-3.5">
                                        <el-tabs
                                            v-model="activeName"
                                            @tab-change="handleTabChange"
                                        >
                                            <template #default>
                                                <!-- <el-tab-pane
                                                    :label="
                                                        $t(
                                                            'home_zhandianliebiao'
                                                        )
                                                    "
                                                    name="station"
                                                >
                                                    <div
                                                        class="table-tabs flex flex-col"
                                                        ref="targetElement"
                                                    >
                                                        <div
                                                            class="page-input-sticky pb-2 pt-2 bg-ff dark:bg-transparent"
                                                        >
                                                            <div
                                                                class="page-input mb-2"
                                                            >
                                                                <el-input
                                                                    v-model="
                                                                        stationName
                                                                    "
                                                                    :placeholder="
                                                                        $t(
                                                                            'placeholder'
                                                                        )
                                                                    "
                                                                    class="input-with-select"
                                                                    clearable
                                                                    @clear="
                                                                        onSearch
                                                                    "
                                                                    @input="
                                                                        onInput
                                                                    "
                                                                >
                                                                </el-input>
                                                                <div
                                                                    class="w-8 h-8 flex justify-center cursor-pointer search-btn"
                                                                    @click="
                                                                        onSearch
                                                                    "
                                                                >
                                                                    <iconSvg
                                                                        name="search"
                                                                        className="icon-search"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div
                                                            class="flex-1 mt-2"
                                                        >
                                                            <div class="">
                                                                <template
                                                                    v-for="(
                                                                        item,
                                                                        index
                                                                    ) in mapList"
                                                                    :key="
                                                                        item.stationId
                                                                    "
                                                                >
                                                                    <car-box
                                                                        @click="
                                                                            clickList(
                                                                                item,
                                                                                index
                                                                            )
                                                                        "
                                                                        :class="
                                                                            active ==
                                                                            index
                                                                                ? 'active-box-list'
                                                                                : ' box-list'
                                                                        "
                                                                        :data="
                                                                            item
                                                                        "
                                                                    />
                                                                </template>
                                                            </div>
                                                            <div
                                                                class="h-80 flex flex-col items-center justify-center"
                                                                v-if="
                                                                    mapList.length <=
                                                                    0
                                                                "
                                                            >
                                                                <empty-data
                                                                    :description="
                                                                        $t(
                                                                            'No Station Bound Yet'
                                                                        )
                                                                    "
                                                                >
                                                                    <slot
                                                                        name="empty"
                                                                    ></slot>
                                                                </empty-data>
                                                                <el-button
                                                                    plain
                                                                    round
                                                                    @click="
                                                                        siteVisible = true
                                                                    "
                                                                    class="btn-hover"
                                                                >
                                                                    <span>{{
                                                                        $t(
                                                                            'AddStation'
                                                                        )
                                                                    }}</span>
                                                                    <span
                                                                        class="icon-box ml-0.5"
                                                                    >
                                                                        <iconSvg
                                                                            name="addSn"
                                                                            class="icon-default"
                                                                        />
                                                                    </span>
                                                                </el-button>
                                                            </div>
                                                            <div
                                                                v-if="
                                                                    mapList.length >=
                                                                    3
                                                                "
                                                                class="text-center leading-12 text-secondar-text dark:text-60-dark"
                                                            >
                                                                {{
                                                                    $t(
                                                                        'No more data'
                                                                    )
                                                                }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-tab-pane> -->
                                                <el-tab-pane
                                                    :label="$t('shebeishitu')"
                                                    name="device"
                                                >
                                                    <div class="table-tabs">
                                                        <div
                                                            class="page-input-sticky px-2 pb-2 pt-2 bg-ff dark:bg-transparent"
                                                        >
                                                            <div
                                                                class="page-input mb-2"
                                                            >
                                                                <el-input
                                                                    v-model="
                                                                        deviceName
                                                                    "
                                                                    :placeholder="
                                                                        $t(
                                                                            'placeholder'
                                                                        )
                                                                    "
                                                                    class="input-with-select"
                                                                    clearable
                                                                    @clear="
                                                                        onDeviceSearch
                                                                    "
                                                                    @input="
                                                                        onDeviceInput
                                                                    "
                                                                >
                                                                </el-input>
                                                                <div
                                                                    class="w-8 h-8 flex justify-center cursor-pointer search-btn"
                                                                    @click="
                                                                        onDeviceSearch
                                                                    "
                                                                >
                                                                    <iconSvg
                                                                        name="search"
                                                                        className="icon-search"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div
                                                                v-for="item in deviceData"
                                                                :key="item.id"
                                                                @click="
                                                                    onClickDevice(
                                                                        item
                                                                    )
                                                                "
                                                                class="cursor-pointer border device-box p-2.5"
                                                            >
                                                                <devicebox
                                                                    :data="item"
                                                                />
                                                            </div>
                                                            <div
                                                                v-if="
                                                                    deviceData.length ===
                                                                    0
                                                                "
                                                                class="mt-20"
                                                            >
                                                                <empty-data
                                                                    :description="
                                                                        $t(
                                                                            'zanwushuju'
                                                                        )
                                                                    "
                                                                >
                                                                    <slot
                                                                        name="empty"
                                                                    ></slot>
                                                                </empty-data>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-tab-pane>
                                                <el-tab-pane
                                                    :label="$t('Metrics View')"
                                                    name="electricity"
                                                >
                                                    <div
                                                        class="table-tabs other-tabs"
                                                    >
                                                        <div class="mb-3">
                                                            <div
                                                                class="charge-title flex justify-between text-title dark:text-title-dark"
                                                            >
                                                                <div
                                                                    class="charge-title-l h-12 leading-12 rounded-l text-sm font-medium"
                                                                >
                                                                    <span
                                                                        class="leading-tight text-left"
                                                                        :class="
                                                                            locale ==
                                                                            'en'
                                                                                ? 'w-25'
                                                                                : ''
                                                                        "
                                                                        >{{
                                                                            $t(
                                                                                'station_zuorichongdianliang'
                                                                            )
                                                                        }}</span
                                                                    >
                                                                    <div
                                                                        class="flex items-baseline"
                                                                    >
                                                                        <span
                                                                            class="text-2.5xl font-bold ml-1 charge-num"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData?.yesterdayCharge ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                        </span>
                                                                        <span
                                                                            class="text-xs charge-unit"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData?.yesterdayCharge ||
                                                                                        0
                                                                                )
                                                                                    .unit
                                                                            }}
                                                                            Ah
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                                <div
                                                                    class="charge-title-r h-12 leading-12 rounded-r text-sm font-medium text-right"
                                                                >
                                                                    <span
                                                                        class="leading-tight text-left"
                                                                        :class="
                                                                            locale ==
                                                                            'en'
                                                                                ? 'w-25'
                                                                                : ''
                                                                        "
                                                                        >{{
                                                                            $t(
                                                                                'station_zuorifangdianliang'
                                                                            )
                                                                        }}</span
                                                                    >
                                                                    <div
                                                                        class="flex items-baseline"
                                                                    >
                                                                        <span
                                                                            class="text-2.5xl font-bold ml-1 charge-num"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData?.yesterdayDischarge ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                        </span>
                                                                        <span
                                                                            class="text-xs charge-unit"
                                                                            >{{
                                                                                formatterAh(
                                                                                    pieceOthenData?.yesterdayDischarge ||
                                                                                        0
                                                                                )
                                                                                    .unit
                                                                            }}Ah</span
                                                                        >
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div
                                                                class="flex justify-between items-center px-2.5"
                                                                style="
                                                                    line-height: 42px;
                                                                    margin-bottom: 3px;
                                                                "
                                                            >
                                                                <div
                                                                    class="flex-1 text-left"
                                                                >
                                                                    <span
                                                                        class="text-secondar-text dark:text-60-dark"
                                                                    >
                                                                        {{
                                                                            pieceOthenData?.beforeYesterdayCharge ==
                                                                            0
                                                                                ? $t(
                                                                                      'station_qianyiriwushuju'
                                                                                  ) +
                                                                                  ' '
                                                                                : $t(
                                                                                      'station_jiaoqianyiri'
                                                                                  ) +
                                                                                  '：'
                                                                        }}
                                                                    </span>
                                                                    <percentage
                                                                        :num="
                                                                            pieceOthenData.comparedChargePercent
                                                                        "
                                                                    />
                                                                </div>
                                                                <div
                                                                    class="flex-1 text-right"
                                                                >
                                                                    <span
                                                                        class="text-secondar-text dark:text-60-dark"
                                                                    >
                                                                        {{
                                                                            pieceOthenData?.beforeYesterdayDischarge ==
                                                                            0
                                                                                ? $t(
                                                                                      'station_qianyiriwushuju'
                                                                                  ) +
                                                                                  ' '
                                                                                : $t(
                                                                                      'station_jiaoqianyiri'
                                                                                  ) +
                                                                                  '：'
                                                                        }}
                                                                    </span>
                                                                    <percentage
                                                                        :num="
                                                                            pieceOthenData.comparedDischargePercent
                                                                        "
                                                                    />
                                                                </div>
                                                            </div>
                                                            <a-divider
                                                                class="m-0"
                                                            />
                                                            <div
                                                                class="flex justify-between items-center px-2.5 leading-4"
                                                                style="
                                                                    margin-top: 13px;
                                                                "
                                                            >
                                                                <div
                                                                    class="flex-1 text-left flex"
                                                                >
                                                                    <div>
                                                                        <div
                                                                            class="text-secondar-text dark:text-60-dark"
                                                                            style="
                                                                                margin-bottom: 9px;
                                                                            "
                                                                        >
                                                                            {{
                                                                                $t(
                                                                                    'station_yueleiji'
                                                                                )
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="font-medium text-sm leading-4 text-title dark:text-title-dark"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData.currentMonthCharge ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                            <span
                                                                                class="text-xs"
                                                                                >{{
                                                                                    formatterAh(
                                                                                        pieceOthenData.currentMonthCharge ||
                                                                                            0
                                                                                    )
                                                                                        .unit
                                                                                }}Ah</span
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="ml-2"
                                                                    >
                                                                        <div
                                                                            class="text-secondar-text dark:text-60-dark"
                                                                            style="
                                                                                margin-bottom: 9px;
                                                                            "
                                                                        >
                                                                            {{
                                                                                $t(
                                                                                    'station_zongji'
                                                                                )
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="font-medium text-sm leading-4 text-title dark:text-title-dark"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData.totalChargeCap ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                            <span
                                                                                class="text-xs"
                                                                                >{{
                                                                                    formatterAh(
                                                                                        pieceOthenData.totalChargeCap ||
                                                                                            0
                                                                                    )
                                                                                        .unit
                                                                                }}Ah</span
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div
                                                                    class="flex-1 text-right flex justify-end"
                                                                >
                                                                    <div>
                                                                        <div
                                                                            class="text-secondar-text dark:text-60-dark"
                                                                            style="
                                                                                margin-bottom: 9px;
                                                                            "
                                                                        >
                                                                            {{
                                                                                $t(
                                                                                    'station_yueleiji'
                                                                                )
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="font-medium text-sm leading-4 text-title dark:text-title-dark"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData.currentMonthDischarge ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                            <span
                                                                                class="text-xs"
                                                                            >
                                                                                {{
                                                                                    formatterAh(
                                                                                        pieceOthenData.currentMonthDischarge ||
                                                                                            0
                                                                                    )
                                                                                        .unit
                                                                                }}
                                                                                Ah</span
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        class="ml-2"
                                                                    >
                                                                        <div
                                                                            class="text-secondar-text dark:text-60-dark"
                                                                            style="
                                                                                margin-bottom: 9px;
                                                                            "
                                                                        >
                                                                            {{
                                                                                $t(
                                                                                    'station_zongji'
                                                                                )
                                                                            }}
                                                                        </div>
                                                                        <div
                                                                            class="font-medium text-sm leading-4 text-title dark:text-title-dark"
                                                                        >
                                                                            {{
                                                                                formatterAh(
                                                                                    pieceOthenData.totalDischargeCap ||
                                                                                        0
                                                                                )
                                                                                    .num
                                                                            }}
                                                                            <span
                                                                                class="text-xs"
                                                                            >
                                                                                {{
                                                                                    formatterAh(
                                                                                        pieceOthenData.totalDischargeCap ||
                                                                                            0
                                                                                    )
                                                                                        .unit
                                                                                }}
                                                                                Ah</span
                                                                            >
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- 图表 -->
                                                        <div
                                                            class="chart-box rounded-lg p-3"
                                                        >
                                                            <div
                                                                class="chart-filter flex items-center justify-between mb-2"
                                                            >
                                                                <div
                                                                    class="pl-2 text-title dark:text-title-dark"
                                                                >
                                                                    {{
                                                                        $t(
                                                                            'station_chongfangdianqushi'
                                                                        )
                                                                    }}
                                                                </div>
                                                                <div
                                                                    class="w-36"
                                                                >
                                                                    <el-select-v2
                                                                        class="w-36"
                                                                        v-model="
                                                                            selectedChargeOption
                                                                        "
                                                                        :placeholder="
                                                                            $t(
                                                                                'placeholder_qingxuanze'
                                                                            )
                                                                        "
                                                                        @change="
                                                                            handleChangeChargeOption
                                                                        "
                                                                        :options="
                                                                            chargeOptions
                                                                        "
                                                                    />
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <charge-charts
                                                                    :chartData="
                                                                        chargeChartData
                                                                    "
                                                                    :chargeSelect="
                                                                        selectedChargeOption ==
                                                                        'year'
                                                                            ? 'month'
                                                                            : 'day'
                                                                    "
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-tab-pane>
                                            </template>
                                        </el-tabs>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="windowInfo bg-ff dark:bg-ff-dark"
                ref="windowInfo"
                id="windowInfo"
                :style="elementStyle"
                v-show="show"
            >
                <div class="flex flex-col mb-3">
                    <div
                        class="title flex justify-between text-title dark:text-title-dark"
                    >
                        <div class="flex mb-3 items-center">
                            <span class="flex sb-icon">
                                <iconSvg name="icon5" class="icon-svg-box" />
                            </span>
                            <div
                                class="my-text-sm text-title dark:text-title-dark"
                            >
                                {{ stationInfo?.stationName || '' }}
                            </div>
                        </div>
                        <div @click="colseWindowInfo" class="closeOutlined">
                            <CloseOutlined style="color: var(--text-60)" />
                        </div>
                    </div>
                    <div class="img-box">
                        <div class="img rounded overflow-hidden">
                            <img
                                :src="stationInfo.stationPic || defaultImg"
                                class="h-full w-full"
                            />
                        </div>
                        <ul
                            class="flex flex-wrap text-title dark:text-title-dark"
                        >
                            <li>
                                <p>
                                    <span>{{
                                        unitConversion(
                                            stationInfo.installedCapacity,
                                            1000
                                        )
                                    }}</span
                                    ><span class="ml-1">{{
                                        alternateUnits(
                                            stationInfo.installedCapacity,
                                            1000
                                        )
                                            ? 'MWh'
                                            : 'kWh'
                                    }}</span>
                                </p>
                                <span class="p-title">{{
                                    $t('station_zhuangjirongliang')
                                }}</span>
                            </li>
                            <li>
                                <p>
                                    <span>{{
                                        unitConversion(
                                            stationInfo.installedPower,
                                            1000
                                        )
                                    }}</span
                                    ><span class="ml-1">{{
                                        alternateUnits(
                                            stationInfo.installedPower,
                                            1000
                                        )
                                            ? 'MW'
                                            : 'kW'
                                    }}</span>
                                </p>
                                <span class="p-title">{{
                                    $t('station_zhuangjigonglv')
                                }}</span>
                            </li>
                            <li>
                                <p>
                                    <span>
                                        {{
                                            stationInfo?.createTime
                                                ? dayjs(
                                                      stationInfo.createTime
                                                  ).format('YYYY/MM/DD')
                                                : ''
                                        }}
                                    </span>
                                </p>
                                <span class="p-title">{{
                                    $t('station_touyunriqi')
                                }}</span>
                            </li>
                        </ul>
                        <div
                            class="ip-unit flex items-center text-title dark:text-title-dark"
                        >
                            <span class="flex">
                                <iconSvg name="ip" class="w-4 h-4 mt-0.5" />
                                <!-- <iconSvg name="dingwei" class="icon-svg-box" /> -->
                            </span>
                            <span class="title-unit">{{
                                stationInfo?.address || ''
                            }}</span>
                        </div>
                    </div>
                </div>
                <div class="pie-echart-box">
                    <div class="flex justify-between">
                        <div>
                            <div
                                v-if="stationInfo.status !== null"
                                class="flex items-center gap-x-1"
                                :style="{
                                    color: getState(stationInfo.status, 'power')
                                        .color,
                                }"
                            >
                                <i
                                    class="w-6 h-6 text-2xl leading-6"
                                    :class="[
                                        'iconfont',
                                        getState(stationInfo.status, 'power')
                                            .icon,
                                    ]"
                                ></i>
                                <span>{{
                                    getState(stationInfo.status, 'power').label
                                        ? $t(
                                              getState(
                                                  stationInfo.status,
                                                  'power'
                                              ).label
                                          )
                                        : ''
                                }}</span>
                            </div>
                        </div>
                        <div style="cursor: pointer">
                            <span v-if="false" class="flex items-center">
                                <span class="flex items-center">
                                    <i
                                        class="iconfont icon-a-ica-dianchi-guzhangbeifen8 icon-fonts"
                                        v-if="stationInfo.alarmQuantity == 0"
                                    ></i>
                                    <i
                                        class="iconfont icon-a-ica-dianchi-guzhangbeifen16 icon-fonts"
                                        style="color: rgb(253, 11, 11)"
                                        v-else
                                    ></i>
                                </span>
                                <span
                                    class="text-t-echart"
                                    :style="{
                                        color:
                                            stationInfo.alarmQuantity == 0
                                                ? '#222222'
                                                : '#FD0B0B',
                                    }"
                                    >{{
                                        stationInfo.alarmQuantity == 0
                                            ? t('wuyichang')
                                            : t('status_yichang')
                                    }}</span
                                >
                                <span
                                    class="text-t-echart my-m-l-1-2"
                                    :style="{
                                        color:
                                            stationInfo.alarmQuantity == 0
                                                ? ''
                                                : '#FD0B0B',
                                    }"
                                    >{{
                                        stationInfo.alarmQuantity == 0
                                            ? ''
                                            : stationInfo.alarmQuantity
                                    }}</span
                                >
                            </span>
                        </div>
                    </div>
                    <div id="chartDischarge" class="chartDischarge"></div>
                    <div
                        class="echart-title my-text-xs text-title dark:text-title-dark"
                        style="width: 100%; text-align: center"
                    >
                        <span class="">SOH「{{ stationInfo.soh }}%」</span
                        ><b>·</b
                        ><span class="">SOC「{{ stationInfo.soc }}%」</span
                        ><b>·</b
                        ><span
                            >{{ $t('Temp') }}「{{
                                stationInfo.avgTemperature
                            }}°C」</span
                        >
                    </div>
                </div>
                <div class="flex justify-center mt-4">
                    <el-button
                        plain
                        round
                        class="info-bt"
                        @click="goDeviceDetail"
                        >{{ $t('station_chakanzhandianxiangqing') }}</el-button
                    >
                </div>
            </div>
        </div>
        <div
            :id="containerId"
            style="
                width: 100vw;
                height: 100vh;
                position: absolute;
                left: 0;
                top: 0;
            "
        ></div>
    </div>
    <el-drawer
        v-model="visible"
        direction="ltr"
        :size="486"
        :show-close="false"
        @close="onClose"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ orgNameValue }}</span>
                </div>
                <div>
                    <el-button plain round @click="onClose">{{
                        $t('common_guanbi')
                    }}</el-button>
                </div>
            </div>
        </template>
        <el-tree
            style="max-width: 600px"
            default-expand-all
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            @node-click="handleNodeClick"
            :current-node-key="selectedKeys[0]"
        />
    </el-drawer>
    <edit-car-info
        v-model:visible="siteVisible"
        @onClose="siteVisible = false"
        :isAddNew="true"
    />
    <el-drawer
        v-model="filterDrawerVisible"
        direction="ltr"
        :size="486"
        :show-close="false"
        @close="onCloseFilterDrawer"
        wrapClassName="drawerBox"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header text-primary-text dark:text-80-dark">
                    <span>{{ '筛选' }}</span>
                </div>
                <div class="flex gap-x-3">
                    <el-button plain round @click="onCloseFilterDrawer">{{
                        $t('common_guanbi')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        @click="onFilterDrawerSave"
                        type="primary"
                        >{{ $t('Confirm') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div>
            <div class="top">
                <el-tabs
                    v-model="activeFilterName"
                    @tab-change="handleFilterTabChange"
                >
                    <template #default>
                        <el-tab-pane label="按项目" name="project">
                            <div class="project-filter pt-4">
                                <div class="project-search">
                                    <el-input
                                        v-model="projectKeywords"
                                        style="max-width: 600px"
                                        :placeholder="
                                            $t('placeholder_qingshuru')
                                        "
                                        class="input-with-select"
                                    >
                                        <template #append>
                                            <el-button @click="onSearchProject">
                                                <el-icon>
                                                    <Search />
                                                </el-icon>
                                            </el-button>
                                        </template>
                                    </el-input>
                                </div>
                                <div class="flex-1">
                                    <el-checkbox
                                        v-model="checkAll"
                                        :indeterminate="isIndeterminate"
                                        @change="handleCheckAllChange"
                                    >
                                        {{ $t('全选') }}
                                    </el-checkbox>
                                    <el-checkbox-group
                                        v-model="checkedProject"
                                        @change="onChangeCheckedProject"
                                        class="column-selector"
                                    >
                                        <el-checkbox
                                            v-for="item in projectList"
                                            :value="item.id"
                                            :key="item.id"
                                        >
                                            {{ item.projectName }}
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </div>
                                <div class="filter-other">
                                    <div>
                                        <el-form
                                            ref="formRef"
                                            :model="formState"
                                            :rules="rules"
                                            :label-width="labelWidth"
                                            label-position="left"
                                        >
                                            <el-form-item
                                                :label="$t('所在城市')"
                                                prop="orgName"
                                            >
                                                <el-select
                                                    v-model="cities"
                                                    :placeholder="
                                                        $t(
                                                            'placeholder_qingxuanze'
                                                        )
                                                    "
                                                    class="w40"
                                                >
                                                    <el-option
                                                        v-for="item in cityOptions"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value"
                                                    />
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item
                                                :label="$t('Device model')"
                                                prop="orgName"
                                            >
                                                <el-select
                                                    v-model="devices"
                                                    :placeholder="
                                                        $t(
                                                            'placeholder_qingxuanze'
                                                        )
                                                    "
                                                    class="w40"
                                                >
                                                    <el-option
                                                        v-for="item in deviceOptions"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value"
                                                    />
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item
                                                :label="$t('激活时间')"
                                                prop="orgName"
                                            >
                                                <el-date-picker
                                                    v-model="createTimeRange"
                                                    type="daterange"
                                                    range-separator="-"
                                                    :start-placeholder="
                                                        $t('common_kaishiriqi')
                                                    "
                                                    :end-placeholder="
                                                        $t('common_jieshuriqi')
                                                    "
                                                    value-format="YYYY-MM-DD"
                                                    class="w40"
                                                />
                                            </el-form-item>
                                        </el-form>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="按客户" name="customer">
                            <div class="table-tabs pt-4"></div>
                        </el-tab-pane>
                        <el-tab-pane label="按分组" name="group">
                            <div class="table-tabs pt-4"></div>
                        </el-tab-pane>
                    </template>
                </el-tabs>
            </div>
        </div>
    </el-drawer>
</template>

<script>
import * as echarts from 'echarts'
import {
    ref,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
    reactive,
    computed,
    onActivated,
    shallowRef,
    watch,
    onDeactivated,
} from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { cloneDeep } from 'lodash'
const img = require('@/assets/device/mapmark.png')
const position = require('@/assets/device/position.png')
const positionActive = require('@/assets/device/position_active.png')
import {
    pieData,
    chargeOptions,
    chargeStatus,
    unitConversion,
    alternateUnits,
    formatterKw,
    formatterAh,
} from '../const'
import apiService from '@/apiService/device'
import carApi from '@/apiService/car'
import powerApi from '@/apiService/power'
import dayjs from 'dayjs'
import _cloneDeep from 'lodash/cloneDeep'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
// import CarBox from './components/carBox.vue'
import Percentage from '@/views/device/components/percentage.vue'
import ChargeCharts from './components/charge.vue'
import moment from 'moment'
import { debounce } from 'lodash-es'
import EditCarInfo from '@/views/device/car/components/editCarInfo.vue'
import { getState, getSegmentTypeColor } from '@/common/util.js'
import { useI18n } from 'vue-i18n'
import Devicebox from './components/devicebox.vue'
import { Filter, Search } from '@element-plus/icons-vue'
export default {
    components: {
        // CarBox,
        Percentage,
        ChargeCharts,
        EditCarInfo,
        Devicebox,
        Filter,
        Search,
    },
    name: 'carOverview',
    setup() {
        const { t, locale } = useI18n()
        const labelWidth = computed(() => {
            let res =
                locale.value == 'zh'
                    ? '70px'
                    : locale.value == 'en'
                    ? '130px'
                    : '140px'
            return res
        })
        const markerContent = `<div class="custom-content-marker" style='width:20px;height:24px'><img style='width:100%;height:100%' src="${position}"></div>`
        const emptyMarkerContent = `<div class="custom-content-marker" style='width:20px;height:24px'></div>`
        const ActiveMarkerContent = `<div class="custom-content-marker" style='width:36px;height:48px'><img style='width:100%;height:100%' src="${positionActive}"></div>`
        const aAMap = ref(null)
        const stationName = ref(void 0)
        const active = ref(void 0)
        const windowInfo = ref(null)
        const mapList = ref([])
        const supplierId = ref(void 0)
        const route = useRoute()
        const store = useStore()
        const show = ref(false)
        const defaultImg = require('@/assets/device/defaultImg.png')
        const mapLoad = () => {
            return new Promise((resovle, resject) => {
                new AMapLoader.load({
                    // key: '83e3b6af5f77f6ad205c468c7cfac8f9',
                    key: '2cbc3120e17cc96ecb0589e8b4c990ce',
                    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
                    plugins: ['AMap.ToolBar', 'AMap.Marker'],
                })
                    .then((AMap) => {
                        resovle(AMap)
                    })
                    .catch((e) => {
                        resject(e)
                    })
            })
        }

        const markerArr = ref([])

        const router = useRouter()

        const addMarker = (list) => {
            list.forEach((item, index) => {
                if (item.longitude && item.latitude) {
                    let marker = new aAMap.value.Marker({
                        position: new aAMap.value.LngLat(
                            item.longitude,
                            item.latitude
                        ),
                        content: markerContent, // 将 html 传给 content
                        offset: new aAMap.value.Pixel(-13, -30), // 以 icon 的 [center bottom] 为原点
                        extData: {
                            index,
                        },
                    })
                    marker.on('click', (e) => {
                        const i = mapList.value.findIndex(
                            (ite) => ite.stationId == item.stationId
                        )
                        // clickList(item, i)
                        let dom = document.getElementById('info-content')
                        dom.scrollTo({
                            top: 254 * i + 1,
                            behavior: 'smooth', // 添加平滑滚动效果
                        })
                        show.value = true
                        active.value = i
                        if (item.longitude && item.latitude) {
                            mapInstance.value &&
                                mapInstance.value.panTo([
                                    item.longitude,
                                    item.latitude,
                                ])
                            createdInfoWindow({
                                lng: item.longitude,
                                lat: item.latitude,
                            })
                        }
                        getEchartsData(item.stationNo)
                        setCenter({ lng: item.longitude, lat: item.latitude })
                        let ind = e.target.getExtData().index
                        markerArr.value.forEach((ite, i1) => {
                            if (i1 === ind) {
                                ite.setContent(ActiveMarkerContent)
                            } else {
                                ite.setContent(markerContent)
                            }
                        })
                    })
                    marker.setMap(mapInstance.value)
                    markerArr.value.push(marker)
                } else {
                    const marker = new aAMap.value.Marker({
                        position: new aAMap.value.LngLat(0, 0),
                        content: emptyMarkerContent, // 将 html 传给 content
                        offset: new aAMap.value.Pixel(-13, -30), // 以 icon 的 [center bottom] 为原点
                        extData: {
                            index,
                        },
                    })
                    marker.setMap(mapInstance.value)
                    markerArr.value.push(marker)
                }
            })
        }
        //第一个图表
        const pieDischargeInit = (soc, value) => {
            soc = soc ? soc : 0
            const option = _cloneDeep(pieData)
            if (soc < 10) {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#FF4D4F',
                ]
                option.series[1].itemStyle.color = '#FF4D4F'
            } else if (soc >= 10 && soc < 30) {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#FD750B',
                ]
                option.series[1].itemStyle.color = '#FD750B '
            } else {
                option.series[0].axisLine.lineStyle.color[0] = [
                    value,
                    '#3EDACD',
                ]
                option.series[1].itemStyle.color = '#3EDACD'
            }
            if (!soc) {
                option.series[1].amplitude = 0
                option.series[1].waveAnimation = 0
            }
            option.series[1].data = [value, value]
            option.series[1].label.formatter = `${soc}%`
            option.series[1].label.fontSize = `20px`
            option.series[0].data[0].detail.formatter = t(
                'station_shengyudianliang'
            )
            initChart(option)
        }
        //获取头部站点信息
        const stationInfo = reactive({
            alarmQuantity: void 0,
            avgTemperature: void 0,
            createTime: void 0,
            address: void 0,
            installedCapacity: void 0,
            installedPower: void 0,
            soc: void 0,
            soh: void 0,
            stationNo: void 0,
            status: void 0,
            stationName: void 0,
        })
        //头部echarts信息
        const getEchartsData = async (stationNo) => {
            const {
                data: { data, code },
            } = await apiService.getStationInfoData({ stationNo })
            if (code === 0) {
                Object.keys(data).forEach((key) => {
                    stationInfo[key] = data[key]
                })
            } else {
                Object.keys(data).forEach((key) => {
                    stationInfo[key] = void 0
                })
            }
            const num = data.soc ? data.soc / 100 : 0
            pieDischargeInit(data.soc, num)
        }
        const createdInfoWindow = (data) => {
            const dom = document.getElementById('windowInfo')
            const infoWindow = new aAMap.value.InfoWindow({
                isCustom: true, //使用自定义窗体
                // content: windowInfo.value, //传入 dom 对象，或者 html 字符串
                content: dom,
                offset: new aAMap.value.Pixel(200, 250),
            })
            if (data.lng && data.lat) {
                infoWindow.open(
                    mapInstance.value,
                    new aAMap.value.LngLat(data.lng, data.lat)
                )
            }
            nextTick(() => {
                echartsInit()
            })
        }

        const echartsInit = () => {
            const data = cloneDeep(pieData)
            data.series[1].label.fontSize = '20px'

            data.series[0].data[0].detail.formatter = t(
                'station_shengyudianliang'
            )
            initChart(data)
        }
        const clickList = (item, index) => {
            router.push({
                name: 'carSystemDetail',
                query: {
                    stationNo: item.stationNo,
                    supplierId: selectedKeys.value[0],
                    stationId: item.stationId,
                },
            })
        }

        const setCenter = (lngData) => {
            if (lngData.lng && lngData.lat) {
                mapInstance.value &&
                    mapInstance.value.setCenter([lngData.lng, lngData.lat])
            }
        }
        const clearMarkers = () => {
            if (markerArr.value.length > 0) {
                markerArr.value.forEach((marker) => {
                    marker.setMap(null)
                })
                markerArr.value = []
            }
        }

        const getMapList = async () => {
            clearMarkers()
            const params = {
                supplierId: selectedKeys.value[0] || supplierId.value,
                stationName: stationName.value,
                current: 1,
                size: 1000,
            }
            // const {
            //     data: { data, code },
            // } = await carApi.getStationPageVehicle(params)
            // if (code === 0) {
            //     mapList.value = data.records
            //     addMarker(mapList.value)
            // }
        }

        const orgInfo = ref()

        const elementStyle = ref({
            transform: 'scale(1)',
        })
        const handleResize = () => {
            const windowHeight = window.innerHeight
            if (windowHeight <= 800) {
                elementStyle.value.transform = 'scale(0.8)'
            } else {
                elementStyle.value.transform = 'scale(1)'
            }
        }
        const fetchData = async () => {
            await getTreeData() // 获取左侧树节点
            await getMapList()
            await getSupplierBmsSummary()
            if (getCompanyInfo.value?.orgName) {
                orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
                    ? store.state.car.selectSupplierInfoCar?.name
                    : getCompanyInfo.value.orgName
            }
            window.addEventListener('resize', handleResize)
        }

        const mapInstance = shallowRef(null)
        const containerId = `map-${Date.now()}` // 防止DOM重复渲染问题
        const mapStyles = {
            dark: 'amap://styles/410c077060e74db5b8673ea8d056e11e',
            light: 'amap://styles/normal',
        }
        const isDark = computed(() => {
            return store.state.theme.isDark
        })
        const mapInit = async () => {
            aAMap.value = await mapLoad()
            mapInstance.value = new aAMap.value.Map(containerId, {
                zoom: 4.8,
                jogEbable: false,
                viewMode: '2D', //是否为3D地图模式
                // center: [253.986154, 43.941266], //初始化地图中心点位置
                center: [95.817119, 36.328107], //初始化地图中心点位置
                mapStyle: isDark.value ? mapStyles.dark : mapStyles.light,
                features: ['bg', 'building', 'point'],
            })
            const toolBar = new aAMap.value.ToolBar({
                visible: false,
            })
            mapInstance.value.addControl(toolBar)
            var disCountry = new aAMap.value.DistrictLayer.Province({
                zIndex: 12,
                // SOC: 'CHN',
                depth: 1,
                styles: {
                    'nation-stroke': '#ff0000',
                    // 'coastline-stroke': '#0088ff',
                    'province-stroke': isDark.value
                        ? '#00D5FF'
                        : 'rgba(99,99,99,0.2)',
                    'city-stroke': 'rgba(99,99,99,0)',
                    'coastline-stroke': isDark.value
                        ? '#76c9db'
                        : 'rgba(99,99,99,1)',
                    fill: function (props) {
                        return props.NAME_CHN
                    },
                },
            })

            // if (orgInfo.value.province) {
            //     mapInstance.value &&
            //         mapInstance.value.setCity(orgInfo.value.province)
            // }
            disCountry.setMap(mapInstance.value)
        }
        const getCitys = async () => {
            cityOptions.value = [
                {
                    label: '上海市',
                    value: 'shanghai',
                },
                {
                    label: '江苏省',
                    value: 'jiangsu',
                },
                {
                    label: '浙江省',
                    value: 'zhejiang',
                },
                {
                    label: '广东省',
                    value: 'guangdong',
                },
            ]
        }
        const cities = ref()
        const cityOptions = ref()
        const devices = ref()
        const deviceOptions = computed(
            () => store.state.dictionary.dictionaries.vehicleType || []
        )
        const createTimeRange = ref([undefined, undefined])

        onMounted(async () => {
            console.log(123123123, '加载加载')

            if (route?.query?.supplierId) {
                supplierId.value = route?.query?.supplierId
            }
            const {
                data: { data, code },
            } = await apiService.getCurrentOrgData()
            orgInfo.value = data
            await mapInit()
            await fetchData()
            await getCitys()
            await store.dispatch('dictionary/getDictionary', 'vehicleType')
            handleResize()
            await getProjectList()
            await getDeviceList()
        })
        onActivated(() => {
            console.log(231313123123123, '加2加2')
            nextTick(async () => {
                mapInstance.value && mapInstance.value.resize()
            })
        })
        const colseWindowInfo = () => {
            mapInstance.value && mapInstance.value.clearInfoWindow()
            active.value = void 0
            markerArr.value.forEach((ite, ind) => {
                ite.setContent(markerContent)
            })
        }

        // onBeforeUnmount(() => {
        //     map && map.destroy()
        //     window.removeEventListener('resize', handleResize)
        //     contentRef.value.removeEventListener('scroll', handleResize)
        // })
        const chartInstance = ref(null)

        const initChart = (option) => {
            if (chartInstance.value) {
                chartInstance.value.dispose()
            }
            chartInstance.value = echarts.init(
                document.getElementById('chartDischarge')
            )
            chartInstance.value.setOption(option)
        }
        onUnmounted(() => {
            if (markerArr.value.length) {
                markerArr.value.forEach((marker) => {
                    marker.setMap(null)
                })
                markerArr.value = []
            }
            if (mapInstance.value) {
                mapInstance.value.destroy()
                mapInstance.value = null
            }
            window.removeEventListener('resize', handleResize)
        })

        const goDeviceDetail = () => {
            router.push({
                path: '/device/deviceDetail',
                query: {
                    stationNo: stationInfo.stationNo,
                    supplierId: stationInfo.customerId,
                    stationId: stationInfo.id,
                },
            })
        }

        // 树图
        const getCompanyInfo = computed(
            () => store.getters['user/getUserInfoData']
        )
        const visible = ref(false)
        const treeData = ref([])
        const selectedKeys = ref([])
        const orgNameValue = ref()
        const handleNodeClick = async (data) => {
            const { id, name, province } = data
            orgNameValue.value = name
            selectedKeys.value = [id]
            visible.value = false
            store.commit('car/setSelectSupplierInfoCar', { id, name, province })
            show.value = false
            // 获取列表
            await getSupplierBmsSummary()
            if (getCompanyInfo.value?.orgName) {
                orgNameValue.value = store.state.car.selectSupplierInfoCar?.id
                    ? store.state.car.selectSupplierInfoCar?.name
                    : getCompanyInfo.value.orgName
            }
            if (activeName.value == 'station') {
                await getMapList()
                if (data.province) {
                    mapInstance.value &&
                        mapInstance.value.setCity(data.province)
                }
            } else if (activeName.value == 'device') {
                await getDeviceList()
            } else if (activeName.value == 'electricity') {
                await getStatisticStationSummaryData()
                await getChargeData()
            }
            hasGetStatisticStationSummaryData.value = false
        }
        const addDraggedProperty = (tree) => {
            function traverse(node) {
                // 新增属性
                // node.noDragging = true
                // 如果节点有子节点，递归遍历子节点
                if (node.children && node.children.length > 0) {
                    node.children.forEach(traverse)
                }
            }
            // 开始遍历树结构
            if (tree?.length) {
                // 开始遍历树结构
                tree.forEach(traverse)
                return tree
            }
        }
        const getTreeData = async () => {
            const {
                data: { data, code },
            } = await apiService.getDeviceTree({
                businessType: 'vehicle_battery',
            })
            if (code === 0) {
                let rootNode = {
                    name: getCompanyInfo?.value?.orgName,
                    id: getCompanyInfo?.value?.orgId,
                    province: getCompanyInfo?.value?.province,
                }
                const tree = [
                    {
                        ...rootNode,
                        children: addDraggedProperty(data) || [],
                    },
                ]
                //
                treeData.value = tree
                selectedKeys.value = store.state.car.selectSupplierInfoCar?.id
                    ? [store.state.car.selectSupplierInfoCar?.id]
                    : [treeData.value[0].id]
                console.log('selectedKeys.value', selectedKeys.value)
                let info = store.state.car.selectSupplierInfoCar?.id
                    ? store.state.car.selectSupplierInfoCar
                    : tree[0]
                const { id, name, province } = info
                store.commit('car/setSelectSupplierInfoCar', {
                    id,
                    name,
                    province,
                })
                // 从树结构中获取当前节点以及下属数据
                // getChartDataByTreeSelectedKey(selectedKeys.value)
            }
        }
        const onClose = () => {
            visible.value = false
        }
        // const activeSystem = ref(localStorage.getItem('activeSystem'))
        const activeSystem = computed(() =>
            localStorage.getItem('activeSystem')
        )

        // tabs选项卡
        const activeName = ref('device')
        const handleTabChange = async (e) => {
            if (e == 'station') {
                //
                // await getMapList()
            } else if (e == 'device') {
                await getDeviceList()
            } else if (e == 'electricity') {
                if (!hasGetStatisticStationSummaryData.value) {
                    await getStatisticStationSummaryData()
                }
                await getChargeData()
            }
        }
        // 列表
        // 统计数据
        const onesPieceData = reactive({
            alarmQuantity: 0,
            installedCapacity: 0,
            installedPower: 0,
            offlineStationQuantity: 0,
            onlineStationQuantity: 0,
            totalStationQuantity: 0,
        })
        const getSupplierBmsSummary = async () => {
            try {
                const {
                    data: { data, code },
                } = await powerApi.getSupplierBmsSummary({
                    supplierId: selectedKeys.value[0] || supplierId.value,
                })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        onesPieceData[key] = data[key] || 0
                    })
                } else {
                    Object.keys(onesPieceData).forEach((key) => {
                        onesPieceData[key] = 0
                    })
                }
            } catch (error) {
                //
            }
        }
        const onInput = debounce((value) => {
            colseWindowInfo()
            getMapList()
        }, 300)
        const onSearch = () => {
            colseWindowInfo()
            getMapList()
        }

        // 充放电统计
        const pieceOthenData = reactive({
            comparedCharge: 0,
            comparedDischarge: 0,
            comparedProfit: 0,
            currentMonthCharge: 0,
            currentMonthDischarge: 0,
            currentMonthProfit: 0,
            chgCapSum: 0,
            dsgCapSum: 0,
            totalProfit: 0,
            yesterdayCharge: 0,
            yesterdayDischarge: 0,
            yesterdayProfit: 0,
        })
        // 充放电统计
        const hasGetStatisticStationSummaryData = ref(false)
        const getStatisticStationSummaryData = async () => {
            hasGetStatisticStationSummaryData.value = true
            try {
                const {
                    data: { data, code },
                } = await powerApi.statsPowerBattUsageSummary({
                    supplierId: selectedKeys.value[0] || supplierId.value,
                    // projectId:,
                })
                if (code === 0) {
                    Object.keys(data).forEach((key) => {
                        pieceOthenData[key] = data[key] || 0
                    })
                } else {
                    Object.keys(pieceOthenData).forEach((key) => {
                        pieceOthenData[key] = 0
                    })
                }
            } catch (error) {
                //
            }
        }
        // 充放电趋势
        const selectedChargeOption = ref('week')
        const chargeChartData = ref(null) //
        // 获取充放电统计数据
        const getChargeData = async () => {
            let params = {}
            if (selectedChargeOption.value == 'week') {
                params = {
                    periodType: 'day',
                    startDate: moment()
                        .subtract(6, 'days')
                        .format('YYYY-MM-DD'),
                    endDate: moment().format('YYYY-MM-DD'),
                }
            } else if (selectedChargeOption.value == 'month') {
                params = {
                    periodType: 'day',
                    startDate: moment()
                        .subtract(29, 'days')
                        .format('YYYY-MM-DD'),
                    endDate: moment().format('YYYY-MM-DD'),
                }
            } else if (selectedChargeOption.value == 'year') {
                params = {
                    periodType: 'month',
                    startMonth: moment()
                        .subtract(11, 'months')
                        .format('YYYY-MM'),
                    endMonth: moment().format('YYYY-MM'),
                }
            }
            const res = await powerApi.statsPowerBattDailyUsage({
                ...params,
                supplierId: selectedKeys.value[0] || supplierId.value,
            })
            if (res.data.data && res.data.data.length) {
                chargeChartData.value = Object.values(res.data.data)
            } else {
                chargeChartData.value = []
                if (selectedChargeOption.value === 'week') {
                    // 生成7天数据
                    for (let i = 6; i >= 0; i--) {
                        chargeChartData.value.push({
                            date: moment()
                                .subtract(i, 'days')
                                .format('YYYY-MM-DD'),
                            chargeCap: null,
                            dischargeCap: null,
                        })
                    }
                } else if (selectedChargeOption.value === 'month') {
                    // 生成30天数据
                    for (let i = 29; i >= 0; i--) {
                        chargeChartData.value.push({
                            date: moment()
                                .subtract(i, 'days')
                                .format('YYYY-MM-DD'),
                            chargeCap: null,
                            dischargeCap: null,
                        })
                    }
                } else if (selectedChargeOption.value === 'year') {
                    // 生成12个月数据
                    for (let i = 11; i >= 0; i--) {
                        chargeChartData.value.push({
                            date: moment()
                                .subtract(i, 'months')
                                .format('YYYY-MM'),
                            chargeCap: null,
                            dischargeCap: null,
                        })
                    }
                }
            }
        }
        const handleChangeChargeOption = async (e) => {
            await getChargeData()
        }
        const toggleSystem = () => {
            if (activeSystem.value == 'car') {
                localStorage.setItem('activeSystem', 'device')
                router.replace({
                    path: '/device',
                })
                // activeSystem.value = 'device'
            } else {
                localStorage.setItem('activeSystem', 'car')
                router.replace({
                    path: '/car',
                })
                // activeSystem.value = 'car'
            }
        }
        const businessType = computed(() => {
            return localStorage.getItem('businessType')
        })
        const showToggleBtn = computed(() => {
            return businessType.value == 'all'
        })

        const isFixed = ref(false)
        const targetElement = ref(null)
        const contentRef = ref(null)
        // const handleScroll = () => {
        //     if (targetElement.value) {
        //         const rect = targetElement.value.getBoundingClientRect()
        //         isFixed.value = rect.top <= 262
        //     }
        // }
        const siteVisible = ref(false)
        watch(
            isDark,
            (newVal) => {
                console.log('nagenagenagenage', newVal)
                if (!mapInstance.value) return
                const style = newVal ? mapStyles.dark : mapStyles.light
                mapInstance.value.setMapStyle(style) // 核心API调用
            },
            { deep: true, immediate: true }
        )

        // 设备试图
        const deviceData = ref([])

        const deviceName = ref('')
        const getDeviceList = async () => {
            const [activeStartDate, activeEndDate] = createTimeRange.value
            let params = {
                current: 1,
                size: 1000,
                activeStartDate,
                activeEndDate,
                keyword: deviceName.value,
                supplierId: selectedKeys.value[0] || supplierId.value,
                // projectId: checkedProject.value[0],
            }
            let res = await powerApi.getDevicePageList(params)
            deviceData.value = res.data.data.records || []
        }
        const onDeviceInput = debounce(async (value) => {
            // 更新地图
            await getDeviceList()
        }, 300)
        const onDeviceSearch = async () => {
            // 更新地图
            //
            await getDeviceList()
        }

        const onClickDevice = (item) => {
            console.log(item)
            router.push({
                name: 'equipmentDetail',
                query: {
                    sn: item.sn,
                    // supplierId: selectedKeys.value[0],
                    id: item.id,
                    customerId: item.customerId,
                },
            })
        }

        onDeactivated(() => {
            // 清理数据
            mapList.value = []
            chargeChartData.value = null
            // 其他数据清理...
        })
        const filterDrawerVisible = ref(false)
        const onCloseFilterDrawer = () => {
            //
            filterDrawerVisible.value = false
        }
        const projectList = ref()
        const getProjectList = async () => {
            let params = {
                current: 1,
                size: 1000,
                keyword: projectKeywords.value,
            }
            let res = await powerApi.getProjectPageList(params)
            projectList.value = res.data.data.records
            checkedProject.value = projectList.value.map((item) => item.id)
        }

        const openFilter = async () => {
            filterDrawerVisible.value = true
        }
        const onFilterDrawerSave = async () => {
            //

            await getDeviceList()
            filterDrawerVisible.value = false
        }
        const activeFilterName = ref('project')
        const handleFilterTabChange = () => {
            //
        }
        const projectKeywords = ref('')
        const checkedProject = ref()
        const onChangeCheckedProject = (e) => {
            //
            console.log(e)

            if (e.length == projectList.value.length) {
                checkAll.value = true
            } else if (e.length == 0) {
                checkAll.value = false
            }
        }
        const checkAll = ref(true)
        const isIndeterminate = ref(false)

        const handleCheckAllChange = (val) => {
            checkedProject.value = val
                ? projectList.value.map((col) => col.id)
                : []

            isIndeterminate.value = false
        }
        const onSearchProject = () => {
            getProjectList()
        }

        return {
            createTimeRange,
            devices,
            deviceOptions,
            cities,
            cityOptions,
            onSearchProject,
            isIndeterminate,
            checkAll,
            checkedProject,
            onChangeCheckedProject,
            handleCheckAllChange,
            projectList,
            projectKeywords,
            activeFilterName,
            handleFilterTabChange,
            onFilterDrawerSave,
            openFilter,
            filterDrawerVisible,
            onCloseFilterDrawer,
            stationName,
            mapList,
            active,
            windowInfo,
            clickList,
            colseWindowInfo,
            onSearch,
            chargeStatus,
            dayjs,
            stationInfo,
            goDeviceDetail,
            unitConversion,
            alternateUnits,
            formatterKw,
            formatterAh,
            supplierId,
            show,
            defaultImg,
            // 树图
            visible,
            selectedKeys,
            treeData,
            orgNameValue,
            defaultProps: {
                label: 'name',
                children: 'children',
            },
            handleNodeClick,
            onClose,
            activeSystem,
            activeName,
            handleTabChange,
            onesPieceData,
            selectedChargeOption,
            chargeOptions,
            handleChangeChargeOption,
            chargeChartData,
            pieceOthenData,
            toggleSystem,
            elementStyle,
            showToggleBtn,
            onInput,
            isFixed,
            targetElement,
            contentRef,
            // handleScroll,
            siteVisible,
            getState,
            containerId,
            chartInstance,
            initChart,
            deviceData,
            deviceName,
            onDeviceInput,
            onDeviceSearch,
            onClickDevice,
            labelWidth,
            locale,
        }
    },
}
</script>

<style lang="less" scoped>
#containerCar {
    width: 100vw;
    height: 100vh;
    .info {
        width: 547px;
        // background: rgba(255, 255, 255, 1);
        border-radius: 8px;
        box-sizing: border-box;
        z-index: 99;
        left: 20px;
        top: 20px;
        padding-top: 68px;
        // height: 50px;
        min-height: calc(~'100vh - 120px');
        .content-list {
            backdrop-filter: blur(10px);
            border: 1px solid #fff;
            border-radius: 8px;
        }
        .info-header {
            border-bottom: 1px solid var(--border);
            margin-bottom: 12px;
            padding: 0 2px;
            padding-bottom: 12px;
            color: #222;
            .orgName {
                color: var(--text-100);
                &:hover {
                    color: var(--themeColor);
                }
            }
        }
        .content-header {
            margin-bottom: 16px;
            .total {
                color: var(--themeColor);
            }
            .device-num-tag {
                // padding: 4px;
                border-radius: 4px;
                font-size: 12px;
                line-height: 24px;
                align-items: center;
                padding: 0 15px;
                font-size: 0;
                height: 24px;
                span {
                    font-size: 12px;
                    vertical-align: baseline;
                }
                .num {
                    font-size: 16px;
                }

                // vertical-align: text-bottom;
            }

            .online {
                background: var(--tag-orange-bg);
                color: var(--tag-orange-color);
            }
            .offline {
                margin-left: 12px;
                background: var(--tag-online-bg);
                color: var(--tag-online-color);
            }
        }
        .content-statistics {
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border);
            margin-bottom: 12px;
            color: var(--text-100);
        }
    }

    .site-search {
        height: 32px;
        width: 180px;
        font-size: 14px;

        :deep(.ant-input-lg) {
            box-sizing: border-box;
            height: 32px;
        }

        :deep(.ant-input-group-addon) {
            .ant-btn-lg {
                height: 32px;
                line-height: 1.215;
            }
        }

        &:hover {
            border-color: var(--themeColor);
        }
    }

    :deep(.ant-input-affix-wrapper-focused) {
        border-color: var(--themeColor);
        box-shadow: none;
    }

    .bg-box {
        background-color: #f5f7f7;
        cursor: pointer;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #f5f7f7;

        &:last-child {
            margin-bottom: 0 !important;
        }
    }
    .box-list {
        border: 1px solid transparent;
    }

    .active-box-list {
        border: 1px solid var(--themeColor);
        box-shadow: 0px 0px 10px 0px rgba(111, 190, 206, 0.2);
    }

    .img-box-list {
        width: 120px;
        height: 90px;
        min-width: 120px;
        border-radius: 4px;
        overflow: hidden;
    }

    .title-color {
        font-size: 0.75rem;
        color: #222222;
    }

    .site-title {
        font-size: 0.75rem;
        overflow: hidden;
        padding-right: 8px;
    }

    .site-title-t {
        color: #222222;
        font-size: 14px;
        width: 100%;
        word-wrap: break-word;
    }

    .site-title-othen {
        color: rgba(34, 34, 34, 0.65);
        font-size: 14px;
        line-height: 22px;
        width: 100%;
        word-wrap: break-word;
    }

    .flex-scoll {
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 6px;
            background-color: #f5f7f7;
        }

        &::-webkit-scrollbar-track {
            border-radius: 6px;
            background-color: #fff;
            border: 1px solid #fff;
        }
    }

    .icon-fonts {
        font-size: 16px;
        vertical-align: middle;
    }
}

.list-content {
    width: 100%;
    margin: 0 auto;
}

.maplist {
    height: calc(~'100vh - 160px');
    overflow-y: auto;
}

.site-list {
    border: 1px solid transparent;

    &:hover {
        border: 1px solid var(--themeColor) !important;
    }

    &.active {
        border: 1px solid var(--themeColor) !important;
    }
}

.site-h {
    // height: 517px;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
    // padding-top: 88px;

    .custom-content-marker {
        position: relative;
        width: 25px;
        height: 34px;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .windowInfo {
        padding: 16px;
        width: 304px;
        box-sizing: border-box;
        // background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        z-index: 10;
        backdrop-filter: blur(10px);
        border: 1px solid #fff;
        h5 {
            margin-left: 4px;
            color: #0e1423;
            font-family: AlibabaPuHuiTi_2_65_Medium;
        }

        .img-box {
            background-color: var(--bg-f5f7fa);
            padding-bottom: 16px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            border-radius: 8px;

            .img {
                height: 204px;
            }

            ul {
                padding: 12px 0;

                li {
                    width: 33.33%;
                    text-align: center;
                    p {
                        font-size: 14px;
                    }

                    .p-title {
                        font-size: 12px;
                    }
                }
            }

            .ip-unit {
                padding-left: 16px;
                font-size: 12px;

                .title-unit {
                    padding-left: 3px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                }
            }
        }

        .closeOutlined {
            cursor: pointer;
            font-size: 12px;
        }

        .pie-echart-box {
            .chartDischarge {
                height: 128px;
            }

            .dianchi {
                font-size: 16px;
                font-weight: 600;
                vertical-align: middle;
            }

            .text-t-echart {
                font-size: 12px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                color: #222222;
                line-height: 22px;
            }
        }

        .info-bt {
            // color: var(--themeColor);
            font-size: 14px;
            padding: 4px 15px;
            height: 32px;

            &:hover {
                border-color: var(--themeColor);
            }
        }
    }

    .icon-svg-box {
        width: 16px;
        height: 17.6px;
    }
}

.amap-copyright,
.amap-logo {
    display: none !important;
}

.my-text-sm {
    font-size: 14px;
    line-height: 20px;
}

.my-text-xs {
    font-size: 12px;
    line-height: 16px;
}

:deep(.el-tree-node:focus > .el-tree-node__content) {
    background-color: transparent;
}
:deep(.el-tree-node__content) {
    flex-direction: row-reverse;
    height: 40px;
    border-radius: 4px;
    .el-tree-node__label {
        line-height: 40px;
        color: var(--text-80);
    }
    .el-text {
        color: var(--text-80) !important;
    }
    &:hover {
        // background: #f5f7f7;
        background-color: var(--bg-f5f7fa);
        color: var(--themeColor);
        .el-tree-node__label {
            &::before {
                border-color: var(--themeColor);
            }
        }
        * {
            fill: var(--themeColor);
        }
        .el-tree-node__expand-icon::before {
            background: var(--themeColor);
        }
        .el-tree-node__expand-icon.expanded::before {
            background: var(--themeColor);
        }
    }
}
:deep(.el-tree-node__label) {
    flex: 1;
    position: relative;
    text-indent: 8px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-bottom-left-radius: 2px;
        border-left: 1px solid var(--border);
        border-bottom: 1px solid var(--border);
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 50%;
        transform: translateY(-50%);
    }
}
:deep(.el-tree) {
    display: block;
}
.el-tree {
    display: block;
    > :deep(.el-tree-node) {
        > .el-tree-node__content {
            > .el-tree-node__label {
                display: block;

                &::before {
                    display: none;
                }
            }
        }
    }
}
:deep(
        .el-tree--highlight-current
            .el-tree-node.is-current
            > .el-tree-node__content
    ) {
    border-radius: 4px;
    > .el-icon {
        color: #fff;
        fill: var(--themeColor);
    }
}
:deep(.el-tree-node) {
    .el-tree-node__expand-icon {
        font-size: 20px;
        color: #fff;
    }

    .el-tree-node__expand-icon::before {
        content: '+';
        font-size: 12px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.expanded::before {
        content: '-';
        font-size: 14px;
        width: 16px;
        height: 16px;
        text-align: center;
        background: rgba(34, 34, 34, 0.16);
        line-height: 16px;
        border-radius: 2px;
    }

    .el-tree-node__expand-icon.is-leaf {
        display: none;
    }
    .el-tree-node__expand-icon.el-icon-caret-right {
        transform: none !important;
    }
    .el-tree-node__expand-icon {
        font-style: normal !important;
        transform: none !important;
        svg {
            display: none !important;
        }
    }
    &.is-current {
        > .el-tree-node__content {
            > .el-tree-node__label {
                color: var(--themeColor);
            }
            > .el-icon {
                color: #fff;
                &:before {
                    background: var(--themeColor);
                }
            }
        }
    }
}
// tabs样式
:deep(.el-tabs__header) {
    padding: 0 6px;
    margin-bottom: 0;
    // position: sticky;
    top: 0;
    z-index: 9999;
    background: #fff;
    padding-bottom: 8px;
}
.dark {
    :deep(.el-tabs__header) {
        background: transparent;
    }
}

:deep(.el-tabs__nav-wrap:after) {
    display: none;
}
:deep(.el-tabs__item) {
    height: 30px;
    padding: 0 16px;
    color: var(--text-100) !important;
    &:hover {
        color: var(--themeColor) !important;
    }
}
:deep(.el-tabs__item.is-active) {
    color: var(--themeColor) !important;
}
:deep(.el-tabs__active-bar) {
    background-color: var(--themeColor);
}
.page-input-sticky {
    // position: sticky;
    // top: 100px;
    z-index: 10;
    // width: 439px;
    // position: absolute;
    // left: 0;
    // top: 0;
    padding-left: 6px;
    padding-right: 6px;
}
.target-element.fixed {
    position: fixed;
    top: 204px;
    /* 其他fixed状态下的样式 */
}
.other-tabs {
    position: relative;
    // top: -56px;
}
.page-input {
    position: relative;
    width: 100%;
    padding: 0;
    .input-with-select {
        position: relative;
        width: 100%;
    }

    .search-btn {
        margin-left: 0;
        position: absolute;
        right: 6px;
        top: 0;
        z-index: 9;
        :deep(.icon-search) {
            width: 20px;
            height: 20px;
            fill: var(--input-color);
        }

        &:hover {
            :deep(.icon-search) {
                fill: var(--themeColor);
            }
        }
    }

    :deep(.el-input__wrapper) {
        padding-right: 32px;
        background: var(--input-bg);
    }

    :deep(.icon-search) {
        width: 20px;
        height: 20px;

        &:hover {
            fill: var(--themeColor);
        }
    }

    :deep(.el-input-group__append) {
        padding: 0;
    }

    :deep(.el-input__suffix) {
        text-align: center;
    }

    :deep(.el-input .el-input__icon) {
        margin: 0 4px;
    }
}
.charge-title {
    position: relative;
    left: -3px;
    margin-top: 8px;
    margin-bottom: 7px;
}
.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 19px');
    position: relative;
    display: flex;
    align-items: center;
}
.charge-num {
    letter-spacing: 0px;
    line-height: 1;
}
.charge-unit {
    line-height: 28px;
}
.charge-title-l {
    background: rgba(51, 190, 79, 0.1);
    padding-left: 12px;
    justify-content: flex-start;
    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 34px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);
    padding-right: 12px;
    justify-content: flex-end;
    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 34px solid transparent;
    }
}

.toggle {
    color: var(--text-100);
    &:hover {
        color: var(--themeColor);
    }
}
.orgName {
    color: var(--themeColor);
}
.dark {
    .orgName {
        color: var(--text-100);
    }
}
.sb-icon {
    color: var(--themeColor);
    margin-right: 4px;
}
.info-content {
    &::-webkit-scrollbar {
        /* Chrome Safari */
        width: 10px;
        /* 横向滚动条宽度为0 */
        height: 15px;
        /* 纵向滚动条高度为0 */
    }
    &::-webkit-scrollbar-thumb {
        background-color: rgba(34, 34, 34, 0.2);
        border-radius: 5px;
    }

    &::-webkit-scrollbar-track {
        background-color: rgba(255, 255, 255, 0);
        border: 1px solid rgba(255, 255, 255, 0);
        border-radius: 5px;
    }

    &::-webkit-scrollbar-button {
        // background-color: #d9d9d9;
        border-radius: 5px;
    }
}
:deep(.el-select__wrapper) {
    background-color: var(--input-bg);
}

:deep(.el-select__placeholder) {
    color: var(--text-80);
}
.echart-title {
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 4px;
    b {
        font-size: 24px;
        line-height: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 4px;
        padding-bottom: 2px;
        display: inline-block;
    }
}
.orgIcon {
    color: var(--themeColor);
}
.project-filter {
    height: calc(100vh - 128px);
    display: flex;
    flex-direction: column;
    .filter-other {
        height: 140px;
    }
}
.device-box {
    border-radius: 4px;
    border: 1px solid var(--border);
    &:hover {
        border: 1px solid var(--themeColor);
    }
}
</style>
<style lang="less">
.el-select-dropdown__item {
    color: var(--text-100);
}
.el-select-dropdown__item.is-selected {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-select-dropdown__item.is-hovering {
    color: var(--themeColor);
    background-color: var(--selected-color);
}
.el-popper.is-light {
    background: #fff;
}
.amap-layer {
    background: #fff;
}
.dark {
    .el-popper {
        border-color: transparent;
    }
    .el-popper.is-light {
        background: var(--main-bg);
    }
    .amap-layer {
        background: var(--main-bg);
    }
    .el-tabs__header {
        background: transparent;
    }
}
:deep(.input-with-select .el-input-group__prepend) {
    background-color: var(--el-fill-color-blank);
}
</style>
