<template>
    <div class="cursor-pointer">
        <div
            class="charge-title flex justify-between text-title dark:text-title-dark"
        >
            <div
                class="charge-title-l h-12 pl-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end"
            >
                <div class="leading-6 text-base">
                    {{ $t('昨日充电时长') }}
                </div>
                <div class="text-3.5xl leading-8 font-bold ml-4.5">
                    {{ data.yesterdayChargeDur }}
                </div>
                <div class="text-xs leading-5 ml-0.5">
                    {{ 'h' }}
                </div>
            </div>
            <div
                class="charge-title-r h-12 pr-3 py-2 leading-12 rounded-l text-sm font-medium flex items-end justify-end"
            >
                <div class="leading-6 text-base">
                    {{ $t('昨日放电时长') }}
                </div>
                <div class="text-3.5xl leading-8 font-bold ml-4.5">
                    {{ data.yesterdayDischargeDur }}
                </div>
                <div class="text-xs leading-5 ml-0.5">
                    {{ 'h' }}
                </div>
            </div>
        </div>
        <div
            class="flex justify-between items-center px-3"
            style="line-height: 42px"
        >
            <div class="flex-1 text-left">
                <span class="text-secondar-text dark:text-60-dark">
                    {{
                        data?.beforeYesterdayChargeDur == 0
                            ? $t('station_qianyiriwushuju')
                            : $t('station_jiaoqianyiri') + '：'
                    }}
                </span>
                <percentage :num="data.comparedChargePercent" class="ml-3" />
            </div>
            <div class="flex-1 text-right">
                <span class="text-secondar-text dark:text-60-dark">
                    {{
                        data?.beforeYesterdayDischargeDur == 0
                            ? $t('station_qianyiriwushuju')
                            : $t('station_jiaoqianyiri') + '：'
                    }}
                </span>
                <percentage :num="data.comparedDischargePercent" class="ml-3" />
            </div>
        </div>
        <a-divider class="m-0" />
        <div class="flex justify-between items-center px-3 mt-3 leading-4">
            <div class="flex-1 text-left flex">
                <div>
                    <span class="text-secondar-text dark:text-60-dark mb-2">
                        {{ $t('station_yueleiji') }}
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.currentMonthChargeDur }}
                        h
                    </span>
                </div>
                <div class="ml-2">
                    <span class="text-secondar-text dark:text-60-dark mb-2">
                        {{ $t('station_zongji') }}
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.totalChargeDur }}
                        h
                    </span>
                </div>
            </div>
            <div class="flex-1 text-right flex justify-end">
                <div>
                    <span class="text-secondar-text dark:text-60-dark mb-2">
                        {{ $t('station_yueleiji') }}
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.currentMonthDischargeDur }}
                        h
                    </span>
                </div>
                <div class="ml-2">
                    <span class="text-secondar-text dark:text-60-dark mb-2">
                        {{ $t('station_zongji') }}
                    </span>
                    <span
                        class="font-medium text-base leading-4 text-title dark:text-title-dark"
                    >
                        {{ data.totalDischargeDur }}
                        h
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import Percentage from './percentage.vue'
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
</script>

<style lang="less" scoped>
.charge-title-l,
.charge-title-r {
    width: calc(~'50% - 24px');
    position: relative;
}

.charge-title-l {
    background: rgba(51, 190, 79, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        left: 100%;
        position: absolute;
        top: 0;
        border-top: 48px solid rgba(51, 190, 79, 0.1);
        border-right: 43px solid transparent;
    }
}

.charge-title-r {
    background: rgba(119, 155, 219, 0.1);

    &::after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        right: 100%;
        position: absolute;
        top: 0;
        border-bottom: 48px solid rgba(119, 155, 219, 0.1);
        border-left: 43px solid transparent;
    }
}
</style>
